{"private": true, "name": "@lasensoft/primitives", "version": "0.1.0", "license": "MIT", "packageManager": "pnpm@8.15.4", "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "test:watch": "turbo run test:watch", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "turbo run typecheck", "clean": "turbo run clean", "reset": "pnpm run clean && rm -rf node_modules", "changeset": "changeset", "version-packages": "changeset version", "release": "pnpm build && changeset publish", "setup-tsconfig": "node scripts/setup-tsconfig.js", "update-packages": "node scripts/update-packages.js"}, "engines": {"node": ">=18"}, "prettier": {"trailingComma": "es5", "printWidth": 100, "singleQuote": true, "tabWidth": 2, "semi": true}, "devDependencies": {"@changesets/cli": "^2.29.4", "@testing-library/react": "^16.3.0", "@types/node": "^22.15.18", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.26.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "npm-check-updates": "^18.0.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^4.1.7", "turbo": "^2.5.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vitest": "^3.1.3"}, "dependencies": {"clsx": "^2.1.1"}}