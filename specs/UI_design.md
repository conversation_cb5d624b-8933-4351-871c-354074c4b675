This outlines the phased development of our UI framework, built from scratch with inspiration from Radix UI's design patterns and Ant Design's feature set, using Atomic Design principles.

## Guiding Principles for Each Component:

For every Atom, Molecule, or Organism:

1.  **Research & Design (Radix/AntD Inspired):**
    *   **Functionality:** Define its core purpose and features.
    *   **API:** Design props (e.g., `variant`, `size`, `disabled`, `value`, `onChange`, `children`, `asChild`), state, and event handlers.
    *   **Accessibility (A11Y):**
        *   Identify required WAI-ARIA roles, states, and properties.
        *   Map out all keyboard interactions.
        *   Define focus management behavior (initial focus, trapping, roving tabindex, etc.).
    *   **Styling Hooks:** Determine necessary `data-*` attributes for styling (e.g., `data-component`, `data-variant`, `data-size`, `data-state`, `data-orientation`).

2.  **Core Implementation (`packages/ui--components-core`):**
    *   Develop the React component using TypeScript.
    *   Implement internal state management (`useState`, `useReducer`, custom hooks).
    *   Add event listeners for mouse, keyboard, touch, and focus events.
    *   Implement all A11Y features (ARIA attributes, keyboard navigation, focus logic).
    *   Render appropriate `data-*` attributes.
    *   Support an `asChild` prop (potentially via a base `Primitive` component you build).

3.  **Testing (`packages/ui--components-core`):**
    *   **Unit Tests (Jest & RTL):** Verify individual logic, state changes, prop effects.
    *   **Interaction Tests (RTL):** Simulate user interactions (keyboard, clicks) and assert correct behavior, ARIA updates, and focus changes.
    *   **Accessibility Tests (`jest-axe`):** Run automated a11y checks.

4.  **Storybook Documentation (`packages/ui--components-core` or shared):**
    *   Create stories for all props, variants, states (disabled, focused, open, etc.).
    *   Demonstrate all keyboard interactions and `asChild` usage.
    *   Initially, these will be unstyled or minimally styled.

5.  **Theming (`packages-theme-tailwind`):**
    *   In the theme's CSS, target the component's `data-*` attributes.
    *   Apply Tailwind utilities (directly or via `@apply`).
    *   Style all variants and states.
    *   Consider using `cva` within the theme for managing style variants if preferred.

6.  **Themed Storybook Update:**
    *   Ensure Storybook (either the core one if it imports theme styles, or a separate theme Storybook) reflects the fully styled component.

---