# Phase 1: Project Setup and Infrastructure

This document outlines the detailed steps for setting up the project infrastructure for the `@lasensoft/primitives` library as a monorepo using Vite.

## 1. Initialize Project Structure

### 1.1 Create Basic Project Structure
```bash
# Create project directory if it doesn't exist already
mkdir -p primitives
cd primitives

# Initialize git repository
git init

# Create initial .gitignore file
cat > .gitignore << EOF
# Dependencies
node_modules
.pnp
.pnp.js

# Build outputs
dist
build
.next
out

# Testing
coverage

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Storybook
storybook-static
EOF

# Create initial README
cat > README.md << EOF
# Lasensoft Primitives

A collection of low-level UI components for building high-quality design systems and web applications.

## Features
- Accessible and WAI-ARIA compliant
- Fully customizable and themeable
- Comprehensive keyboard navigation
- Thoroughly tested
- TypeScript-first development experience

## Installation

\`\`\`bash
# With npm
npm install @lasensoft/primitives

# With yarn
yarn add @lasensoft/primitives

# With pnpm
pnpm add @lasensoft/primitives
\`\`\`

## Documentation
See our documentation site for detailed usage examples and API references.

## License
MIT
EOF

# Create initial commit
git add .gitignore README.md
git commit -m "Initial commit: Project setup"
```

## 2. Configure PNPM Workspace for Monorepo

### 2.1 Set Up PNPM Workspace
```bash
# Create pnpm workspace configuration
cat > pnpm-workspace.yaml << EOF
packages:
  - 'packages/*'
  - 'apps/*'
  - 'internal/*'
EOF

# Create .npmrc file
cat > .npmrc << EOF
node-linker=hoisted
strict-peer-dependencies=false
auto-install-peers=true
EOF
```

### 2.2 Create Root Package.json
```bash
# Create root package.json
cat > package.json << EOF
{
  "private": true,
  "name": "@lasensoft/primitives",
  "version": "0.1.0",
  "license": "MIT",
  "scripts": {
    "dev": "pnpm --filter=@lasensoft/storybook dev",
    "build": "pnpm -r --parallel --filter \"./packages/**/*\" build",
    "test": "vitest run",
    "test:watch": "vitest",
    "lint": "eslint .",
    "lint:fix": "eslint --fix .",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "typecheck": "pnpm -r --parallel typecheck",
    "clean": "pnpm -r run clean",
    "reset": "pnpm run clean && rm -rf node_modules",
    "changeset": "changeset",
    "version-packages": "changeset version",
    "release": "pnpm build && changeset publish"
  },
  "engines": {
    "node": ">=18"
  },
  "prettier": {
    "trailingComma": "es5",
    "printWidth": 100,
    "singleQuote": true,
    "tabWidth": 2,
    "semi": true
  },
  "devDependencies": {
    "@changesets/cli": "^2.26.0",
    "@testing-library/react": "^14.0.0",
    "@types/node": "^18.0.0",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "@typescript-eslint/eslint-plugin": "^5.59.0",
    "@typescript-eslint/parser": "^5.59.0",
    "@vitejs/plugin-react": "^4.0.0",
    "eslint": "^8.38.0",
    "eslint-plugin-react": "^7.32.2",
    "eslint-plugin-react-hooks": "^4.6.0",
    "prettier": "^2.8.7",
    "typescript": "^5.0.4",
    "vite": "^4.3.1",
    "vite-plugin-dts": "^2.3.0",
    "vitest": "^0.30.1"
  }
}
EOF
```

### 2.3 Create Directory Structure
```bash
# Create main package directories
mkdir -p packages/core
mkdir -p packages/react

# Create application directories
mkdir -p apps/docs
mkdir -p apps/storybook

# Create supporting directories
mkdir -p internal/build
mkdir -p internal/test-utils
mkdir -p internal/tsconfig

# Create utility directories
mkdir -p scripts
mkdir -p .changeset
```

## 3. Configure TypeScript

### 3.1 Set Up TypeScript Configuration
```bash
# Create base TypeScript configuration
cat > tsconfig.json << EOF
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "jsx": "react-jsx",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "isolatedModules": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "baseUrl": ".",
    "paths": {
      "@lasensoft/core": ["packages/core/src"],
      "@lasensoft/react/*": ["packages/react/*/src"]
    }
  },
  "exclude": ["node_modules", "dist"]
}
EOF
```

### 3.2 Create Shared TypeScript Configurations
```bash
# Create shared TypeScript configurations
mkdir -p internal/tsconfig
cat > internal/tsconfig/base.json << EOF
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "display": "Default",
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "jsx": "react-jsx",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  }
}
EOF

cat > internal/tsconfig/react-library.json << EOF
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "display": "React Library",
  "extends": "./base.json",
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "isolatedModules": true
  }
}
EOF

cat > internal/tsconfig/app.json << EOF
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "display": "Application",
  "extends": "./base.json",
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "jsx": "react-jsx",
    "noEmit": true
  }
}
EOF
```

## 4. Configure ESLint and Prettier

### 4.1 Set Up ESLint
```bash
# Create ESLint configuration
cat > .eslintrc.js << EOF
module.exports = {
  root: true,
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:@typescript-eslint/recommended'
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['react', 'react-hooks', '@typescript-eslint'],
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },
  settings: {
    react: {
      version: 'detect'
    }
  },
  rules: {
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
    '@typescript-eslint/no-unused-vars': ['warn', { 'argsIgnorePattern': '^_' }],
    '@typescript-eslint/explicit-module-boundary-types': 'off'
  }
};
EOF
```

### 4.2 Set Up Prettier
```bash
# Create Prettier configuration
cat > .prettierrc << EOF
{
  "trailingComma": "es5",
  "printWidth": 100,
  "singleQuote": true,
  "tabWidth": 2,
  "semi": true
}
EOF

cat > .prettierignore << EOF
node_modules
dist
build
coverage
.next
out
storybook-static
pnpm-lock.yaml
EOF
```

## 5. Configure Testing with Vitest

### 5.1 Set Up Vitest Configuration
```bash
# Create Vitest configuration
cat > vitest.config.ts << EOF
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./internal/test-utils/setup.ts'],
    coverage: {
      reporter: ['text', 'json', 'html']
    }
  },
  resolve: {
    alias: {
      '@lasensoft/core': resolve(__dirname, 'packages/core/src'),
      '@lasensoft/react': resolve(__dirname, 'packages/react')
    }
  }
});
EOF

# Create test setup file
mkdir -p internal/test-utils
cat > internal/test-utils/setup.ts << EOF
import '@testing-library/jest-dom';
EOF
```

## 6. Set Up Core Package

### 6.1 Configure Core Package
```bash
# Create core package structure
mkdir -p packages/core/src
mkdir -p packages/core/tests

# Create core package.json
cat > packages/core/package.json << EOF
{
  "name": "@lasensoft/core",
  "version": "0.1.0",
  "license": "MIT",
  "source": "./src/index.ts",
  "main": "./dist/index.js",
  "module": "./dist/index.mjs",
  "types": "./dist/index.d.ts",
  "files": [
    "dist",
    "README.md"
  ],
  "scripts": {
    "build": "vite build",
    "lint": "eslint src",
    "lint:fix": "eslint --fix src",
    "clean": "rm -rf dist",
    "typecheck": "tsc --noEmit",
    "test": "vitest run",
    "test:watch": "vitest"
  },
  "peerDependencies": {
    "react": "^16.8 || ^17.0 || ^18.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.0",
    "react": "^18.0.0",
    "typescript": "^5.0.4",
    "vite": "^4.3.1",
    "vitest": "^0.30.1"
  }
}
EOF

# Create core package vite config
cat > packages/core/vite.config.ts << EOF
import { defineConfig } from 'vite';
import { resolve } from 'path';
import dts from 'vite-plugin-dts';

export default defineConfig({
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'LasensoftCore',
      fileName: (format) => \`index.\${format === 'es' ? 'mjs' : 'js'}\`,
    },
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
        },
      },
    },
  },
  plugins: [dts()],
});
EOF

# Create core package tsconfig
cat > packages/core/tsconfig.json << EOF
{
  "extends": "../../internal/tsconfig/react-library.json",
  "include": ["src"],
  "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.test.tsx"],
  "compilerOptions": {
    "outDir": "./dist"
  }
}
EOF

# Create core package README
cat > packages/core/README.md << EOF
# @lasensoft/core

Core utilities and shared functionality for the Lasensoft Primitives component library.

## Installation

\`\`\`bash
npm install @lasensoft/core
\`\`\`

## Usage

\`\`\`tsx
import { composeEventHandlers } from '@lasensoft/core';

// Use utilities in your components
\`\`\`
EOF

# Create core package index file
cat > packages/core/src/index.ts << EOF
// Core utilities will be exported here
export const version = '0.1.0';
EOF
```

## 7. Set Up React Components Package

### 7.1 Configure React Primitive Component
```bash
# Create react package structure
mkdir -p packages/react/primitive/src
mkdir -p packages/react/primitive/tests

# Create primitive package.json
cat > packages/react/primitive/package.json << EOF
{
  "name": "@lasensoft/react-primitive",
  "version": "0.1.0",
  "license": "MIT",
  "source": "./src/index.ts",
  "main": "./dist/index.js",
  "module": "./dist/index.mjs",
  "types": "./dist/index.d.ts",
  "files": [
    "dist",
    "README.md"
  ],
  "scripts": {
    "build": "vite build",
    "lint": "eslint src",
    "lint:fix": "eslint --fix src",
    "clean": "rm -rf dist",
    "typecheck": "tsc --noEmit",
    "test": "vitest run",
    "test:watch": "vitest"
  },
  "dependencies": {
    "@lasensoft/core": "workspace:*"
  },
  "peerDependencies": {
    "react": "^16.8 || ^17.0 || ^18.0",
    "react-dom": "^16.8 || ^17.0 || ^18.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "typescript": "^5.0.4",
    "vite": "^4.3.1",
    "vitest": "^0.30.1"
  }
}
EOF

# Create primitive package vite config
cat > packages/react/primitive/vite.config.ts << EOF
import { defineConfig } from 'vite';
import { resolve } from 'path';
import react from '@vitejs/plugin-react';
import dts from 'vite-plugin-dts';

export default defineConfig({
  plugins: [react(), dts()],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'LasensoftReactPrimitive',
      fileName: (format) => \`index.\${format === 'es' ? 'mjs' : 'js'}\`,
    },
    rollupOptions: {
      external: ['react', 'react-dom', '@lasensoft/core'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
          '@lasensoft/core': 'LasensoftCore',
        },
      },
    },
  },
});
EOF

# Create primitive package tsconfig
cat > packages/react/primitive/tsconfig.json << EOF
{
  "extends": "../../../internal/tsconfig/react-library.json",
  "include": ["src"],
  "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.test.tsx"],
  "compilerOptions": {
    "outDir": "./dist"
  }
}
EOF

# Create primitive package README
cat > packages/react/primitive/README.md << EOF
# @lasensoft/react-primitive

A low-level primitive component for building other UI components.

## Installation

\`\`\`bash
npm install @lasensoft/react-primitive
\`\`\`

## Usage

\`\`\`tsx
import { Primitive } from '@lasensoft/react-primitive';

// Use as a div
<Primitive.div>Content</Primitive.div>

// Use as a button
<Primitive.button onClick={() => console.log('clicked')}>
  Click me
</Primitive.button>
\`\`\`
EOF

# Create primitive package index file
cat > packages/react/primitive/src/index.ts << EOF
// Primitive component will be implemented here
export const version = '0.1.0';
EOF
```

## 8. Set Up Storybook

### 8.1 Configure Storybook
```bash
# Create storybook app structure
mkdir -p apps/storybook/.storybook
mkdir -p apps/storybook/src/stories

# Create storybook package.json
cat > apps/storybook/package.json << EOF
{
  "name": "@lasensoft/storybook",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "storybook dev -p 6006",
    "build": "storybook build",
    "clean": "rm -rf storybook-static"
  },
  "dependencies": {
    "@lasensoft/core": "workspace:*",
    "@lasensoft/react-primitive": "workspace:*",
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "devDependencies": {
    "@storybook/addon-essentials": "^7.0.0",
    "@storybook/addon-interactions": "^7.0.0",
    "@storybook/addon-links": "^7.0.0",
    "@storybook/blocks": "^7.0.0",
    "@storybook/react": "^7.0.0",
    "@storybook/react-vite": "^7.0.0",
    "@storybook/testing-library": "^0.0.14-next.2",
    "storybook": "^7.0.0",
    "typescript": "^5.0.4",
    "vite": "^4.3.1"
  }
}
EOF

# Create storybook configuration
cat > apps/storybook/.storybook/main.ts << EOF
import type { StorybookConfig } from '@storybook/react-vite';

const config: StorybookConfig = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|ts|tsx)'],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
  ],
  framework: {
    name: '@storybook/react-vite',
    options: {},
  },
  docs: {
    autodocs: 'tag',
  },
};

export default config;
EOF

cat > apps/storybook/.storybook/preview.ts << EOF
import type { Preview } from '@storybook/react';

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
  },
};

export default preview;
EOF

# Create a sample story
cat > apps/storybook/src/stories/Introduction.stories.mdx << EOF
import { Meta } from '@storybook/blocks';

<Meta title="Introduction/Welcome" />

# Welcome to Lasensoft Primitives

Lasensoft Primitives is a collection of low-level UI components for building high-quality design systems and web applications.

## Features
- Accessible and WAI-ARIA compliant
- Fully customizable and themeable
- Comprehensive keyboard navigation
- Thoroughly tested
- TypeScript-first development experience

## Getting Started

Browse the components in the sidebar to see examples and documentation.
EOF
```

## 9. Initialize Project

```bash
# Install dependencies
pnpm install

# Run initial build
pnpm build

# Start Storybook for development
pnpm dev
```

## 10. Conclusion

This completes the setup for Phase 1 of the @lasensoft/primitives library. The project now has:

1. A monorepo structure using PNPM workspaces
2. TypeScript configuration for both library and application packages
3. Vite build system for fast development and optimized production builds
4. ESLint and Prettier for code quality and consistent formatting
5. Vitest for testing
6. Core package structure for shared utilities
7. React primitive component package structure
8. Storybook for component development and documentation

In the next phase, we'll implement the core utilities and foundational components that will serve as the building blocks for more complex UI components.

## Next Steps

1. Implement core utilities (event handlers, hooks, etc.)
2. Develop foundational components (Primitive, VisuallyHidden, Portal, etc.)
3. Set up documentation site
4. Configure CI/CD pipeline