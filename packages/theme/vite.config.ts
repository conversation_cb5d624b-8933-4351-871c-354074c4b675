import { createPackageConfig } from '../../internal/build/vite-config';
import { defineConfig } from 'vite';
import { resolve } from 'path';
import react from '@vitejs/plugin-react';
import dts from 'vite-plugin-dts';

// Create a custom config that includes CSS handling
const baseConfig = createPackageConfig({
  packageName: '@lasensoft/theme',
  globalName: 'LasensoftTheme',
  additionalExternals: ["@lasensoft/core"],
  additionalGlobals: {
    "@lasensoft/core": "LasensoftCore"
  },
  dtsOptions: {
    tsconfigPath: './tsconfig.json'
  }
});

// Extend the base config to handle CSS files
export default defineConfig({
  ...baseConfig,
  build: {
    ...baseConfig.build,
    rollupOptions: {
      ...baseConfig.build?.rollupOptions,
      output: {
        ...baseConfig.build?.rollupOptions?.output,
        assetFileNames: 'assets/[name][extname]',
      },
    },
  },
});
