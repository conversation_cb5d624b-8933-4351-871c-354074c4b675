# @lasensoft/theme

Theming system for the Lasensoft Primitives component library.

## Installation

```bash
npm install @lasensoft/theme
```

## Features

- Design tokens for consistent styling
- CSS variables for theming
- Theme provider for easy theme switching
- Support for light and dark modes

## Usage

### Basic Usage

```tsx
import { ThemeProvider } from '@lasensoft/theme';
import { lightTheme } from '@lasensoft/theme/tokens';

function App() {
  return (
    <ThemeProvider theme={lightTheme}>
      <YourApp />
    </ThemeProvider>
  );
}
```

### Theme Switching

```tsx
import { ThemeProvider, useTheme } from '@lasensoft/theme';
import { lightTheme, darkTheme } from '@lasensoft/theme/tokens';

function ThemeSwitcher() {
  const { theme, setTheme } = useTheme();
  
  return (
    <button onClick={() => setTheme(theme === lightTheme ? darkTheme : lightTheme)}>
      Toggle Theme
    </button>
  );
}

function App() {
  return (
    <ThemeProvider defaultTheme={lightTheme}>
      <ThemeSwitcher />
      <YourApp />
    </ThemeProvider>
  );
}
```

## Customization

You can create custom themes by extending the base themes:

```tsx
import { createTheme } from '@lasensoft/theme';
import { lightTheme } from '@lasensoft/theme/tokens';

const customTheme = createTheme({
  ...lightTheme,
  colors: {
    ...lightTheme.colors,
    primary: '#ff0000',
  },
});
```
