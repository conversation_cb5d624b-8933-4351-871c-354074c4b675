{"name": "@lasensoft/theme", "version": "0.1.0", "license": "MIT", "source": "./src/index.ts", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"build": "vite build", "lint": "eslint src", "lint:fix": "eslint --fix src", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dependencies": {"@lasensoft/core": "workspace:*"}, "devDependencies": {"@types/react": "^18.0.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "react": "^18.0.0", "tailwindcss": "^4.1.7", "typescript": "^5.0.4", "vite": "^4.3.1", "vitest": "^0.30.1"}}