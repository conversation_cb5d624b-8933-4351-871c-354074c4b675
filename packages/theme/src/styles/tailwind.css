@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-primary: #1677ff;
    --color-secondary: #6b7280;
    --color-success: #52c41a;
    --color-warning: #faad14;
    --color-error: #ff4d4f;
    --color-info: #1677ff;
    
    --color-text-primary: #000000e0;
    --color-text-secondary: #00000073;
    --color-text-disabled: #00000040;
    
    --color-background-default: #ffffff;
    --color-background-paper: #ffffff;
    --color-background-subtle: #f5f5f5;
    
    --color-border-default: #d9d9d9;
    --color-border-light: #f0f0f0;
    --color-border-focus: #1677ff;
    
    --spacing-0: 0;
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.5rem;
    --spacing-6: 2rem;
    --spacing-7: 2.5rem;
    --spacing-8: 3rem;
    
    --font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    --font-family-heading: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-family-mono: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    --line-height-none: 1;
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    --line-height-loose: 2;
    
    --radius-none: 0;
    --radius-sm: 0.125rem;
    --radius-md: 0.25rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-full: 9999px;
    
    --shadow-none: none;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    --z-index-hide: -1;
    --z-index-auto: auto;
    --z-index-base: 0;
    --z-index-dropdown: 1000;
    --z-index-sticky: 1100;
    --z-index-fixed: 1200;
    --z-index-overlay: 1300;
    --z-index-modal: 1400;
    --z-index-popover: 1500;
    --z-index-tooltip: 1600;
    
    --breakpoint-xs: 0px;
    --breakpoint-sm: 600px;
    --breakpoint-md: 900px;
    --breakpoint-lg: 1200px;
    --breakpoint-xl: 1536px;
    --breakpoint-2xl: 1920px;
  }

  .dark {
    --color-primary: #1668dc;
    
    --color-text-primary: #ffffffd9;
    --color-text-secondary: #ffffff73;
    --color-text-disabled: #ffffff40;
    
    --color-background-default: #141414;
    --color-background-paper: #1f1f1f;
    --color-background-subtle: #303030;
    
    --color-border-default: #424242;
    --color-border-light: #303030;
    --color-border-focus: #1668dc;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-primary text-white hover:bg-opacity-90 focus:ring-primary;
  }
  
  .btn-secondary {
    @apply bg-secondary text-white hover:bg-opacity-90 focus:ring-secondary;
  }
  
  .btn-outline {
    @apply border border-border-default text-text-primary hover:bg-bg-subtle focus:ring-primary;
  }
  
  .btn-sm {
    @apply text-sm px-3 py-1;
  }
  
  .btn-lg {
    @apply text-lg px-5 py-3;
  }
}
