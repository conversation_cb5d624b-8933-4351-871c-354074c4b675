import { ThemeTokens } from './index';

/**
 * Converts theme tokens to CSS variables
 */
export function tokensToCssVariables(theme: ThemeTokens): Record<string, string> {
  return {
    // Colors
    '--color-primary': theme.colors.primary,
    '--color-secondary': theme.colors.secondary,
    '--color-success': theme.colors.success,
    '--color-warning': theme.colors.warning,
    '--color-error': theme.colors.error,
    '--color-info': theme.colors.info,
    
    // Text colors
    '--color-text-primary': theme.colors.text.primary,
    '--color-text-secondary': theme.colors.text.secondary,
    '--color-text-disabled': theme.colors.text.disabled,
    
    // Background colors
    '--color-background-default': theme.colors.background.default,
    '--color-background-paper': theme.colors.background.paper,
    '--color-background-subtle': theme.colors.background.subtle,
    
    // Border colors
    '--color-border-default': theme.colors.border.default,
    '--color-border-light': theme.colors.border.light,
    '--color-border-focus': theme.colors.border.focus,
    
    // Spacing
    '--spacing-0': theme.spacing[0],
    '--spacing-1': theme.spacing[1],
    '--spacing-2': theme.spacing[2],
    '--spacing-3': theme.spacing[3],
    '--spacing-4': theme.spacing[4],
    '--spacing-5': theme.spacing[5],
    '--spacing-6': theme.spacing[6],
    '--spacing-7': theme.spacing[7],
    '--spacing-8': theme.spacing[8],
    
    // Typography
    '--font-family-base': theme.typography.fontFamily.base,
    '--font-family-heading': theme.typography.fontFamily.heading,
    '--font-family-mono': theme.typography.fontFamily.mono,
    
    '--font-weight-light': theme.typography.fontWeight.light.toString(),
    '--font-weight-regular': theme.typography.fontWeight.regular.toString(),
    '--font-weight-medium': theme.typography.fontWeight.medium.toString(),
    '--font-weight-semibold': theme.typography.fontWeight.semibold.toString(),
    '--font-weight-bold': theme.typography.fontWeight.bold.toString(),
    
    '--font-size-xs': theme.typography.fontSize.xs,
    '--font-size-sm': theme.typography.fontSize.sm,
    '--font-size-md': theme.typography.fontSize.md,
    '--font-size-lg': theme.typography.fontSize.lg,
    '--font-size-xl': theme.typography.fontSize.xl,
    '--font-size-2xl': theme.typography.fontSize['2xl'],
    '--font-size-3xl': theme.typography.fontSize['3xl'],
    
    '--line-height-none': theme.typography.lineHeight.none.toString(),
    '--line-height-tight': theme.typography.lineHeight.tight.toString(),
    '--line-height-normal': theme.typography.lineHeight.normal.toString(),
    '--line-height-relaxed': theme.typography.lineHeight.relaxed.toString(),
    '--line-height-loose': theme.typography.lineHeight.loose.toString(),
    
    // Radii
    '--radius-none': theme.radii.none,
    '--radius-sm': theme.radii.sm,
    '--radius-md': theme.radii.md,
    '--radius-lg': theme.radii.lg,
    '--radius-xl': theme.radii.xl,
    '--radius-full': theme.radii.full,
    
    // Shadows
    '--shadow-none': theme.shadows.none,
    '--shadow-sm': theme.shadows.sm,
    '--shadow-md': theme.shadows.md,
    '--shadow-lg': theme.shadows.lg,
    '--shadow-xl': theme.shadows.xl,
    
    // Z-indices
    '--z-index-hide': theme.zIndices.hide.toString(),
    '--z-index-auto': theme.zIndices.auto,
    '--z-index-base': theme.zIndices.base.toString(),
    '--z-index-dropdown': theme.zIndices.dropdown.toString(),
    '--z-index-sticky': theme.zIndices.sticky.toString(),
    '--z-index-fixed': theme.zIndices.fixed.toString(),
    '--z-index-overlay': theme.zIndices.overlay.toString(),
    '--z-index-modal': theme.zIndices.modal.toString(),
    '--z-index-popover': theme.zIndices.popover.toString(),
    '--z-index-tooltip': theme.zIndices.tooltip.toString(),
    
    // Breakpoints
    '--breakpoint-xs': theme.breakpoints.xs,
    '--breakpoint-sm': theme.breakpoints.sm,
    '--breakpoint-md': theme.breakpoints.md,
    '--breakpoint-lg': theme.breakpoints.lg,
    '--breakpoint-xl': theme.breakpoints.xl,
    '--breakpoint-2xl': theme.breakpoints['2xl'],
  };
}

/**
 * Converts CSS variables to a style object
 */
export function cssVariablesToStyle(variables: Record<string, string>): React.CSSProperties {
  return variables as unknown as React.CSSProperties;
}
