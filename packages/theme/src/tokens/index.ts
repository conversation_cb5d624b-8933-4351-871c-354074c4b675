/**
 * Design tokens for the theme system
 */

export interface ThemeTokens {
  colors: ColorTokens;
  spacing: SpacingTokens;
  typography: TypographyTokens;
  radii: RadiiTokens;
  shadows: ShadowTokens;
  zIndices: ZIndexTokens;
  breakpoints: BreakpointTokens;
}

export interface ColorTokens {
  // Base colors
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  
  // Text colors
  text: {
    primary: string;
    secondary: string;
    disabled: string;
  };
  
  // Background colors
  background: {
    default: string;
    paper: string;
    subtle: string;
  };
  
  // Border colors
  border: {
    default: string;
    light: string;
    focus: string;
  };
}

export interface SpacingTokens {
  0: string;
  1: string;
  2: string;
  3: string;
  4: string;
  5: string;
  6: string;
  7: string;
  8: string;
}

export interface TypographyTokens {
  fontFamily: {
    base: string;
    heading: string;
    mono: string;
  };
  fontWeight: {
    light: number;
    regular: number;
    medium: number;
    semibold: number;
    bold: number;
  };
  fontSize: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
  };
  lineHeight: {
    none: number;
    tight: number;
    normal: number;
    relaxed: number;
    loose: number;
  };
}

export interface RadiiTokens {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  full: string;
}

export interface ShadowTokens {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

export interface ZIndexTokens {
  hide: number;
  auto: string;
  base: number;
  dropdown: number;
  sticky: number;
  fixed: number;
  overlay: number;
  modal: number;
  popover: number;
  tooltip: number;
}

export interface BreakpointTokens {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
}

/**
 * Light theme tokens
 */
export const lightTheme: ThemeTokens = {
  colors: {
    primary: '#1677ff',
    secondary: '#6b7280',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1677ff',
    
    text: {
      primary: '#000000e0',
      secondary: '#00000073',
      disabled: '#00000040',
    },
    
    background: {
      default: '#ffffff',
      paper: '#ffffff',
      subtle: '#f5f5f5',
    },
    
    border: {
      default: '#d9d9d9',
      light: '#f0f0f0',
      focus: '#1677ff',
    },
  },
  
  spacing: {
    0: '0',
    1: '0.25rem',
    2: '0.5rem',
    3: '0.75rem',
    4: '1rem',
    5: '1.5rem',
    6: '2rem',
    7: '2.5rem',
    8: '3rem',
  },
  
  typography: {
    fontFamily: {
      base: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      heading: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      mono: 'SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
    },
    fontWeight: {
      light: 300,
      regular: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      md: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
    },
    lineHeight: {
      none: 1,
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
      loose: 2,
    },
  },
  
  radii: {
    none: '0',
    sm: '0.125rem',
    md: '0.25rem',
    lg: '0.5rem',
    xl: '0.75rem',
    full: '9999px',
  },
  
  shadows: {
    none: 'none',
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  },
  
  zIndices: {
    hide: -1,
    auto: 'auto',
    base: 0,
    dropdown: 1000,
    sticky: 1100,
    fixed: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    tooltip: 1600,
  },
  
  breakpoints: {
    xs: '0px',
    sm: '600px',
    md: '900px',
    lg: '1200px',
    xl: '1536px',
    '2xl': '1920px',
  },
};

/**
 * Dark theme tokens
 */
export const darkTheme: ThemeTokens = {
  ...lightTheme,
  colors: {
    ...lightTheme.colors,
    primary: '#1668dc',
    
    text: {
      primary: '#ffffffd9',
      secondary: '#ffffff73',
      disabled: '#ffffff40',
    },
    
    background: {
      default: '#141414',
      paper: '#1f1f1f',
      subtle: '#303030',
    },
    
    border: {
      default: '#424242',
      light: '#303030',
      focus: '#1668dc',
    },
  },
};

/**
 * Create a custom theme by extending an existing theme
 */
export function createTheme(theme: Partial<ThemeTokens>): ThemeTokens {
  return {
    ...lightTheme,
    ...theme,
    colors: {
      ...lightTheme.colors,
      ...theme.colors,
    },
  };
}
