import * as React from 'react';
import { createContext } from '@lasensoft/core';
import { ThemeTokens, lightTheme } from '../tokens';

export interface ThemeContextValue {
  /**
   * Current theme
   */
  theme: ThemeTokens;
  
  /**
   * Set the current theme
   */
  setTheme: (theme: ThemeTokens) => void;
}

export const [ThemeProvider, useThemeContext, ThemeContext] = createContext<ThemeContextValue>({
  name: 'ThemeContext',
  errorMessage: 'useThemeContext must be used within a ThemeProvider',
});

/**
 * Hook to access the current theme and theme setter
 */
export function useTheme(): ThemeContextValue {
  return useThemeContext();
}
