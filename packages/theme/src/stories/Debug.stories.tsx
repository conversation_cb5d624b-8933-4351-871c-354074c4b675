import * as React from 'react';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta = {
  title: 'Theme/Debug',
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj;

const DebugComponent = () => {
  return (
    <div style={{ padding: '20px', backgroundColor: '#f0f0f0', borderRadius: '8px' }}>
      <h1 style={{ color: '#333', fontSize: '24px' }}>Debug Component</h1>
      <p style={{ color: '#666' }}>This is a simple component for debugging Storybook issues.</p>
      <button style={{ 
        padding: '8px 16px', 
        backgroundColor: '#0070f3', 
        color: 'white', 
        border: 'none', 
        borderRadius: '4px',
        cursor: 'pointer'
      }}>
        Test Button
      </button>
    </div>
  );
};

export const Default: Story = {
  render: () => <DebugComponent />,
};
