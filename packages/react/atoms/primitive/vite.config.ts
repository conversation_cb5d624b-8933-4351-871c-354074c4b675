import { createPackageConfig } from '../../../../internal/build/vite-config';

export default createPackageConfig({
  packageName: '@lasensoft/react-primitive',
  globalName: 'LasensoftReactPrimitive',
  additionalExternals: ["@lasensoft/react-slot","@lasensoft/react-utils"],
  additionalGlobals: {
  "@lasensoft/react-slot": "LasensoftReactSlot",
  "@lasensoft/react-utils": "LasensoftReactUtils"
},
  dtsOptions: {
    tsconfigPath: './tsconfig.json'
  }
});