import * as React from 'react';
import { Slot } from '@lasensoft/react-slot';
import { PrimitiveComponentMap, PrimitiveProps, PrimitiveTag, SupportedPrimitiveTag } from './Primitive.types';

/**
 * Creates a Primitive component for the specified HTML tag
 */
function createPrimitive<T extends SupportedPrimitiveTag>(tag: T) {
  const Component = React.forwardRef<HTMLElement, PrimitiveProps<T>>(
    ({ asChild, ...props }, forwardedRef) => {
      const Comp = asChild ? Slot : tag;
      // Use type assertion to handle the complex type relationships
      return React.createElement(Comp as any, { ...props, ref: forwardedRef });
    }
  );

  Component.displayName = `Primitive.${tag}`;
  return Component;
}

/**
 * A set of primitive components that can be used as the base for building other components.
 * Each primitive component accepts an `asChild` prop which allows it to be replaced with another element.
 */
const Primitive = {} as PrimitiveComponentMap;

// Create a primitive component for each supported HTML tag
const primitiveElements: SupportedPrimitiveTag[] = [
  'a',
  'button',
  'div',
  'form',
  'h1',
  'h2',
  'h3',
  'h4',
  'h5',
  'h6',
  'header',
  'img',
  'input',
  'label',
  'li',
  'nav',
  'ol',
  'p',
  'span',
  'svg',
  'ul',
];

primitiveElements.forEach((tag) => {
  (Primitive as any)[tag] = createPrimitive(tag);
});

export { Primitive };
