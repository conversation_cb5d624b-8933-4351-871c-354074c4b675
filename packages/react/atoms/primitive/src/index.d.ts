import * as React from 'react';

/**
 * HTML tags that can be used with Primitive
 */
export type PrimitiveTag = keyof JSX.IntrinsicElements;

/**
 * Subset of HTML tags that we explicitly support
 */
export type SupportedPrimitiveTag =
  | 'a'
  | 'button'
  | 'div'
  | 'form'
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
  | 'h6'
  | 'header'
  | 'img'
  | 'input'
  | 'label'
  | 'li'
  | 'nav'
  | 'ol'
  | 'p'
  | 'span'
  | 'svg'
  | 'ul';

/**
 * Props that are common to all Primitive components
 */
export interface PrimitivePropsWithoutRef<T extends PrimitiveTag> {
  /**
   * When true, the component will render its children directly without wrapping them in a DOM element.
   * The first child will receive all props and refs that would have been passed to the component.
   */
  asChild?: boolean;
}

/**
 * Props for a Primitive component, including all props for the specified HTML tag
 */
export type PrimitiveProps<T extends PrimitiveTag> = PrimitivePropsWithoutRef<T> &
  Omit<JSX.IntrinsicElements[T], 'ref'> &
  React.RefAttributes<HTMLElementTagNameMap[T]>;

/**
 * A map of HTML tags to their corresponding Primitive components
 */
export type PrimitiveComponentMap = {
  [T in SupportedPrimitiveTag]: React.ForwardRefExoticComponent<PrimitiveProps<T>>;
};

/**
 * A set of primitive components that can be used as the base for building other components.
 * Each primitive component accepts an `asChild` prop which allows it to be replaced with another element.
 */
export const Primitive: PrimitiveComponentMap;
