import * as React from 'react';
import { forwardRef } from 'react';
import { Primitive } from '@lasensoft/react-primitive';
import clsx from 'clsx';
import { ButtonProps } from './Button.types';

/**
 * Button component
 * 
 * A fundamental UI component for triggering actions or events.
 * 
 * @example
 * ```tsx
 * <Button variant="primary" size="medium" onClick={() => console.log('clicked')}>
 *   Click me
 * </Button>
 * ```
 */
const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      variant = 'default',
      size = 'medium',
      disabled = false,
      loading = false,
      className,
      asChild,
      ...props
    },
    forwardedRef
  ) => {
    return (
      <Primitive.button
        ref={forwardedRef}
        disabled={disabled || loading}
        data-component="button"
        data-variant={variant}
        data-size={size}
        data-disabled={disabled || loading ? '' : undefined}
        data-loading={loading ? '' : undefined}
        className={clsx(className)}
        asChild={asChild}
        {...props}
      >
        {children}
      </Primitive.button>
    );
  }
);

Button.displayName = 'Button';

export { Button };
