import * as React from 'react';
import { PrimitiveProps } from '@lasensoft/react-primitive';

/**
 * Button variants
 */
export type ButtonVariant = 'default' | 'primary' | 'secondary' | 'ghost' | 'link';

/**
 * Button sizes
 */
export type ButtonSize = 'small' | 'medium' | 'large';

/**
 * Button props
 */
export interface ButtonProps extends PrimitiveProps<'button'> {
  /**
   * The visual variant of the button
   * @default 'default'
   */
  variant?: ButtonVariant;

  /**
   * The size of the button
   * @default 'medium'
   */
  size?: ButtonSize;

  /**
   * Whether the button is disabled
   * @default false
   */
  disabled?: boolean;

  /**
   * Whether the button is in a loading state
   * @default false
   */
  loading?: boolean;

  /**
   * Additional CSS class names
   */
  className?: string;

  /**
   * The content of the button
   */
  children: React.ReactNode;

  /**
   * Event handler called when the button is clicked
   */
  onClick?: React.MouseEventHandler<HTMLButtonElement>;

  /**
   * When true, the component will render its children directly without wrapping them in a DOM element.
   * The first child will receive all props and refs that would have been passed to the component.
   */
  asChild?: boolean;
}
