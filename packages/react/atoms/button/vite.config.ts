import { createPackageConfig } from '../../../../internal/build/vite-config';

export default createPackageConfig({
  packageName: '@lasensoft/react-button',
  globalName: 'LasensoftReactButton',
  additionalExternals: ["clsx","@lasensoft/react-primitive","@lasensoft/react-utils"],
  additionalGlobals: {
  "clsx": "clsx",
  "@lasensoft/react-primitive": "LasensoftReactPrimitive",
  "@lasensoft/react-utils": "LasensoftReactUtils"
},
  dtsOptions: {
    tsconfigPath: './tsconfig.json'
  }
});