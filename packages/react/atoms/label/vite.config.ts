import { createPackageConfig } from '../../../../internal/build/vite-config';

export default createPackageConfig({
  packageName: '@lasensoft/react-label',
  globalName: 'LasensoftReactLabel',
  additionalExternals: ["@lasensoft/react-primitive","@lasensoft/react-utils"],
  additionalGlobals: {
  "@lasensoft/react-primitive": "LasensoftReactPrimitive",
  "@lasensoft/react-utils": "LasensoftReactUtils"
},
  dtsOptions: {
    tsconfigPath: './tsconfig.json'
  }
});