import * as React from 'react';
import { forwardRef } from 'react';
import { Primitive } from '@lasensoft/react-primitive';
import { composeEventHandlers } from '@lasensoft/core';
import { LabelProps } from './Label.types';

/**
 * Label component
 * 
 * A component for rendering a label that can be associated with a form control.
 * Extends the native label element with additional functionality like preventing text selection on double-click.
 * 
 * @example
 * ```tsx
 * <Label htmlFor="email">Email</Label>
 * <input id="email" type="email" />
 * ```
 */
const Label = forwardRef<HTMLLabelElement, LabelProps>(
  (
    {
      children,
      preventTextSelectionOnDoubleClick = true,
      onDoubleClick,
      ...props
    },
    forwardedRef
  ) => {
    // Handle double-click to prevent text selection if enabled
    const handleDoubleClick = React.useCallback(
      (event: React.MouseEvent<HTMLLabelElement>) => {
        if (preventTextSelectionOnDoubleClick) {
          event.preventDefault();
        }
      },
      [preventTextSelectionOnDoubleClick]
    );

    return (
      <Primitive.label
        ref={forwardedRef}
        data-component="label"
        onDoubleClick={preventTextSelectionOnDoubleClick ? composeEventHandlers(onDoubleClick, handleDoubleClick) : onDoubleClick}
        {...props}
      >
        {children}
      </Primitive.label>
    );
  }
);

Label.displayName = 'Label';

export { Label };
