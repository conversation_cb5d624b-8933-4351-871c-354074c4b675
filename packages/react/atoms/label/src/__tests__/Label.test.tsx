import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect } from 'vitest';
import { Label } from '../Label';

describe('Label', () => {
  it('renders correctly', () => {
    render(<Label>Test Label</Label>);
    expect(screen.getByText('Test Label')).toBeInTheDocument();
  });

  it('forwards htmlFor attribute', () => {
    render(<Label htmlFor="test-id">Test Label</Label>);
    expect(screen.getByText('Test Label')).toHaveAttribute('for', 'test-id');
  });

  it('calls onDoubleClick handler', async () => {
    const handleDoubleClick = vi.fn();
    render(<Label onDoubleClick={handleDoubleClick}>Test Label</Label>);
    await userEvent.dblClick(screen.getByText('Test Label'));
    expect(handleDoubleClick).toHaveBeenCalledTimes(1);
  });

  it('prevents default on double-click when preventTextSelectionOnDoubleClick is true', async () => {
    const handleDoubleClick = vi.fn();
    render(
      <Label preventTextSelectionOnDoubleClick={true} onDoubleClick={handleDoubleClick}>
        Test Label
      </Label>
    );

    const label = screen.getByText('Test Label');
    const preventDefaultSpy = vi.spyOn(MouseEvent.prototype, 'preventDefault');

    await userEvent.dblClick(label);

    expect(handleDoubleClick).toHaveBeenCalledTimes(1);
    expect(preventDefaultSpy).toHaveBeenCalled();

    preventDefaultSpy.mockRestore();
  });

  it('does not prevent default on double-click when preventTextSelectionOnDoubleClick is false', async () => {
    const handleDoubleClick = vi.fn();
    render(
      <Label preventTextSelectionOnDoubleClick={false} onDoubleClick={handleDoubleClick}>
        Test Label
      </Label>
    );

    const label = screen.getByText('Test Label');
    const preventDefaultSpy = vi.spyOn(MouseEvent.prototype, 'preventDefault');

    await userEvent.dblClick(label);

    expect(handleDoubleClick).toHaveBeenCalledTimes(1);
    expect(preventDefaultSpy).not.toHaveBeenCalled();

    preventDefaultSpy.mockRestore();
  });

  it('forwards ref to label element', () => {
    const ref = React.createRef<HTMLLabelElement>();
    render(<Label ref={ref}>Test Label</Label>);
    expect(ref.current).toBeInstanceOf(HTMLLabelElement);
  });

  it('applies data-component attribute', () => {
    render(<Label>Test Label</Label>);
    expect(screen.getByText('Test Label')).toHaveAttribute('data-component', 'label');
  });
});
