import * as React from 'react';
import { PrimitiveProps } from '@lasensoft/react-primitive';

/**
 * Label props
 */
export interface LabelProps extends PrimitiveProps<'label'> {
  /**
   * Whether to prevent text selection when double-clicking
   * @default true
   */
  preventTextSelectionOnDoubleClick?: boolean;

  /**
   * The HTML for attribute
   */
  htmlFor?: string;

  /**
   * The content of the label
   */
  children: React.ReactNode;

  /**
   * Event handler called when the label is double-clicked
   */
  onDoubleClick?: React.MouseEventHandler<HTMLLabelElement>;
}
