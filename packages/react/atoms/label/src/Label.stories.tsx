import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { Label } from './Label';

const meta: Meta<typeof Label> = {
  title: 'Atoms/Label',
  component: Label,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    preventTextSelectionOnDoubleClick: {
      control: 'boolean',
      description: 'Whether to prevent text selection when double-clicking',
      table: {
        defaultValue: { summary: 'true' },
      },
    },
    htmlFor: {
      control: 'text',
      description: 'The ID of the form control this label is associated with',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Label>;

export const Default: Story = {
  args: {
    children: 'Label Text',
    htmlFor: 'example-input',
  },
  render: (args) => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
      <Label {...args} />
      <input id="example-input" placeholder="Input associated with label" />
    </div>
  ),
};

export const WithoutPreventingTextSelection: Story = {
  args: {
    children: 'Double-click to select text',
    preventTextSelectionOnDoubleClick: false,
  },
};

export const WithPreventingTextSelection: Story = {
  args: {
    children: 'Double-click will not select text',
    preventTextSelectionOnDoubleClick: true,
  },
};
