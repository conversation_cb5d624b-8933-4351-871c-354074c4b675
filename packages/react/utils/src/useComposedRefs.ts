import * as React from 'react';
import { composeRefs } from './composeRefs';

/**
 * A hook that composes multiple React refs into a single ref callback.
 * The returned callback will update whenever any of the provided refs change.
 * 
 * @example
 * ```tsx
 * const Button = forwardRef((props, forwardedRef) => {
 *   const innerRef = useRef(null);
 *   const composedRef = useComposedRefs(innerRef, forwardedRef);
 *   
 *   return <button {...props} ref={composedRef} />;
 * });
 * ```
 */
export function useComposedRefs<T>(...refs: React.Ref<T>[]) {
  return React.useCallback(composeRefs(...refs), refs);
}
