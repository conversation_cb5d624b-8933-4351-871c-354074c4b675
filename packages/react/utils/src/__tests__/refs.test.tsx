import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { composeRefs, useComposedRefs } from '../refs';

describe('ref utilities', () => {
  describe('composeRefs', () => {
    it('should handle callback refs', () => {
      const callbackRef = vi.fn();
      const composedRef = composeRefs(callbackRef);
      const element = document.createElement('div');
      
      composedRef(element);
      
      expect(callbackRef).toHaveBeenCalledWith(element);
    });
    
    it('should handle object refs', () => {
      const objRef = { current: null };
      const composedRef = composeRefs(objRef);
      const element = document.createElement('div');
      
      composedRef(element);
      
      expect(objRef.current).toBe(element);
    });
    
    it('should handle multiple refs', () => {
      const callbackRef = vi.fn();
      const objRef1 = { current: null };
      const objRef2 = { current: null };
      const composedRef = composeRefs(callbackRef, objRef1, objRef2);
      const element = document.createElement('div');
      
      composedRef(element);
      
      expect(callbackRef).toHaveBeenCalledWith(element);
      expect(objRef1.current).toBe(element);
      expect(objRef2.current).toBe(element);
    });
    
    it('should handle undefined refs', () => {
      const callbackRef = vi.fn();
      const composedRef = composeRefs(callbackRef, undefined);
      const element = document.createElement('div');
      
      composedRef(element);
      
      expect(callbackRef).toHaveBeenCalledWith(element);
    });
  });
  
  describe('useComposedRefs', () => {
    function TestComponent({ 
      refs, 
      testId = 'test-element' 
    }: { 
      refs: React.Ref<HTMLDivElement>[], 
      testId?: string 
    }) {
      const composedRef = useComposedRefs(...refs);
      return <div data-testid={testId} ref={composedRef} />;
    }
    
    it('should compose multiple refs', () => {
      const callbackRef = vi.fn();
      const objRef = React.createRef<HTMLDivElement>();
      
      render(<TestComponent refs={[callbackRef, objRef]} />);
      
      const element = screen.getByTestId('test-element');
      expect(callbackRef).toHaveBeenCalledWith(element);
      expect(objRef.current).toBe(element);
    });
    
    it('should handle undefined refs', () => {
      const callbackRef = vi.fn();
      
      render(<TestComponent refs={[callbackRef, undefined]} />);
      
      const element = screen.getByTestId('test-element');
      expect(callbackRef).toHaveBeenCalledWith(element);
    });
  });
});
