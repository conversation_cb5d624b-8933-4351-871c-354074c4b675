import * as React from 'react';

type PossibleRef<T> = React.Ref<T> | undefined;

/**
 * Combines multiple React refs into a single ref function.
 * 
 * @example
 * ```tsx
 * const Button = forwardRef((props, forwardedRef) => {
 *   const innerRef = useRef(null);
 *   const composedRef = composeRefs(innerRef, forwardedRef);
 *   
 *   return <button {...props} ref={composedRef} />;
 * });
 * ```
 */
export function composeRefs<T>(...refs: PossibleRef<T>[]) {
  return (node: T | null) => {
    refs.forEach((ref) => {
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref != null) {
        (ref as React.MutableRefObject<T | null>).current = node;
      }
    });
  };
}
