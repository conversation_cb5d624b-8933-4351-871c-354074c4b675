# @lasensoft/react-utils

Utility functions and hooks for React components.

## Installation

```bash
npm install @lasensoft/react-utils
```

## Usage

```tsx
import { useComposedRefs } from '@lasensoft/react-utils';

function MyComponent() {
  const localRef = React.useRef(null);
  const composedRef = useComposedRefs(localRef, props.forwardedRef);
  
  return <div ref={composedRef}>Content</div>;
}
```

## Available Utilities

### Ref Utilities

- **setRef**: Set a value to a ref (handles both callback refs and RefObject types)
- **composeRefs**: Combine multiple refs into a single ref callback
- **useComposedRefs**: A React hook that uses composeRefs to create a memoized ref callback
