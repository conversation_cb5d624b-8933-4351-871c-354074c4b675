# @lasensoft/react-portal

A component for rendering content outside the current DOM hierarchy.

## Installation

```bash
npm install @lasensoft/react-portal
```

## Usage

```tsx
import { Portal } from '@lasensoft/react-portal';

// Render content at the end of the document body
<Portal>
  <div className="modal">Modal content</div>
</Portal>

// Render content in a specific container
<Portal container={document.getElementById('modal-root')}>
  <div className="modal">Modal content</div>
</Portal>
```

## API

### Portal

A component that renders its children into a DOM node that exists outside the DOM hierarchy of the parent component.

#### Props

- `container`: The DOM node to render the children into. Defaults to document.body.
- All other props are passed to the underlying div element.

### Root

An alias for Portal.
