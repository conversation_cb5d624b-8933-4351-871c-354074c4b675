{"name": "@lasensoft/react-portal", "version": "0.1.0", "license": "MIT", "source": "./src/index.ts", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"build": "vite build", "lint": "eslint src", "lint:fix": "eslint --fix src", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@lasensoft/core": "workspace:*", "@lasensoft/react-hooks": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "devDependencies": {"@internal/tsconfig": "workspace:*", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.1.3"}}