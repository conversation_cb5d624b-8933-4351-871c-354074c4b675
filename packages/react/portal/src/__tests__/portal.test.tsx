import * as React from 'react';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Portal } from '../portal';

describe('Portal', () => {
  let portalRoot: HTMLElement;

  beforeEach(() => {
    // Create a custom container for the portal
    portalRoot = document.createElement('div');
    portalRoot.setAttribute('id', 'portal-root');
    document.body.appendChild(portalRoot);
  });

  afterEach(() => {
    // Clean up the custom container
    if (portalRoot.parentElement) {
      document.body.removeChild(portalRoot);
    }
  });

  it('renders children into the document body by default', () => {
    render(
      <Portal>
        <div data-testid="portal-content">Portal Content</div>
      </Portal>
    );

    // The portal content should be in the document
    const portalContent = screen.getByTestId('portal-content');
    expect(portalContent).toBeInTheDocument();

    // The portal content should be a child of the body
    expect(portalContent.parentElement).toBe(document.body);
  });

  it('renders children into the specified container', () => {
    render(
      <Portal container={portalRoot}>
        <div data-testid="portal-content">Portal Content</div>
      </Portal>
    );

    // The portal content should be in the document
    const portalContent = screen.getByTestId('portal-content');
    expect(portalContent).toBeInTheDocument();

    // The portal content should be a child of the portal root
    expect(portalContent.parentElement).toBe(portalRoot);
  });

  it('does not render when container is null', () => {
    render(
      <Portal container={null}>
        <div data-testid="portal-content">Portal Content</div>
      </Portal>
    );

    // The portal content should not be in the document
    expect(screen.queryByTestId('portal-content')).not.toBeInTheDocument();
  });
});
