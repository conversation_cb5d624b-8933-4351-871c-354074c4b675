import React, { useRef, useState } from 'react';
import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Portal } from './portal';

const meta: Meta<typeof Portal> = {
  title: 'Utility/Portal',
  component: Portal,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    container: {
      control: 'object',
      description: 'The DOM node to render the children into. Defaults to document.body.',
    },
    children: {
      control: 'text',
      description: 'The content to render in the portal.',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Portal>;

export const Default: Story = {
  render: () => {
    return (
      <div style={{ border: '1px solid #ccc', padding: '1rem', position: 'relative' }}>
        <h3>Parent Container</h3>
        <p>The content below is rendered through a portal to the document body:</p>

        <Portal>
          <div
            style={{
              position: 'fixed',
              bottom: '20px',
              right: '20px',
              padding: '1rem',
              background: '#f0f0f0',
              border: '1px solid #ccc',
              borderRadius: '4px',
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)'
            }}
          >
            This content is rendered at the end of the document body
          </div>
        </Portal>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'By default, Portal renders its children at the end of the document body.',
      },
    },
  },
};

export const CustomContainer: Story = {
  render: () => {
    const [containerRef, setContainerRef] = useState<HTMLDivElement | null>(null);

    return (
      <div style={{ display: 'flex', gap: '2rem' }}>
        <div style={{ border: '1px solid #ccc', padding: '1rem', width: '300px' }}>
          <h3>Source Container</h3>
          <p>This content is rendered normally in the DOM hierarchy.</p>

          <Portal container={containerRef}>
            <div style={{ padding: '1rem', background: '#e6f7ff', border: '1px solid #91d5ff', borderRadius: '4px' }}>
              This content is portaled to the target container
            </div>
          </Portal>
        </div>

        <div
          ref={setContainerRef}
          style={{
            border: '1px solid #ccc',
            padding: '1rem',
            width: '300px',
            background: '#f9f9f9'
          }}
        >
          <h3>Target Container</h3>
          <p>The blue box below is rendered through a portal from the source container:</p>
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'You can specify a custom container to render the portal content into.',
      },
    },
  },
};
