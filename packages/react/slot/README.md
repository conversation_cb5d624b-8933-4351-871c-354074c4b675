# @lasensoft/react-slot

A utility component for component composition through slotting.

## Installation

```bash
npm install @lasensoft/react-slot
```

## Usage

```tsx
import { Slot, Slottable } from '@lasensoft/react-slot';

// Basic usage - passing props to children
function Button(props) {
  return (
    <Slot {...props}>
      <button>Click me</button>
    </Slot>
  );
}

// Usage with custom element
<Button className="custom-button">
  Custom content
</Button>

// Advanced usage with Slottable
function Dialog({ trigger, content }) {
  return (
    <>
      <Slot>
        <button>{trigger}</button>
      </Slot>
      <div className="content">
        <Slottable>{content}</Slottable>
      </div>
    </>
  );
}

// Usage
<Dialog 
  trigger={<span>Open dialog</span>}
  content={<div>Dialog content</div>}
/>
```

## API

### Slot

A component that passes its props to its child element.

```tsx
<Slot className="my-class">
  <button>Click me</button>
</Slot>
```

### Slottable

A component that marks its children to be slotted into a specific location.

```tsx
<Slottable>
  <div>This content will be slotted</div>
</Slottable>
```

### createSlot

A function to create a custom Slot component with a specific name.

```tsx
const CustomSlot = createSlot('Custom');
```

### createSlottable

A function to create a custom Slottable component with a specific name.

```tsx
const CustomSlottable = createSlottable('Custom');
```
