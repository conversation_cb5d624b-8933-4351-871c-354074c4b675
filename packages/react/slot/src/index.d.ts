import * as React from 'react';

export interface SlotProps extends React.HTMLAttributes<HTMLElement> {
  children?: React.ReactNode;
}

export interface SlottableProps {
  children: React.ReactNode;
}

/**
 * Creates a Slot component with a specific owner name for better debugging
 */
export declare function createSlot(ownerName: string): React.ForwardRefExoticComponent<SlotProps & React.RefAttributes<HTMLElement>>;

/**
 * Creates a Slottable component with a specific owner name for better debugging
 */
export declare function createSlottable(ownerName: string): React.FC<SlottableProps> & { __lasensoftId: symbol };

/**
 * Default Slot component
 */
export declare const Slot: React.ForwardRefExoticComponent<SlotProps & React.RefAttributes<HTMLElement>>;

/**
 * Default Slottable component
 */
export declare const Slottable: React.FC<SlottableProps> & { __lasensoftId: symbol };

/**
 * Alias for Slot
 */
export declare const Root: React.ForwardRefExoticComponent<SlotProps & React.RefAttributes<HTMLElement>>;
