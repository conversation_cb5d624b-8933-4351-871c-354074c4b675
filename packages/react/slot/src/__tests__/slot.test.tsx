import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Slot, Slottable } from '../slot';

describe('Slot', () => {
  it('should render children', () => {
    render(
      <Slot>
        <button data-testid="button">Click me</button>
      </Slot>
    );
    
    expect(screen.getByTestId('button')).toBeInTheDocument();
    expect(screen.getByTestId('button').textContent).toBe('Click me');
  });
  
  it('should pass props to children', () => {
    render(
      <Slot className="test-class" data-testid="slot">
        <button>Click me</button>
      </Slot>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('test-class');
    expect(button).toHaveAttribute('data-testid', 'slot');
  });
  
  it('should merge className props', () => {
    render(
      <Slot className="slot-class">
        <button className="button-class">Click me</button>
      </Slot>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('slot-class');
    expect(button).toHaveClass('button-class');
  });
  
  it('should merge style props', () => {
    render(
      <Slot style={{ color: 'red', fontSize: '16px' }}>
        <button style={{ backgroundColor: 'blue', fontSize: '20px' }}>Click me</button>
      </Slot>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveStyle({
      color: 'red',
      backgroundColor: 'blue',
      fontSize: '20px', // Child's fontSize overrides Slot's fontSize
    });
  });
  
  it('should compose event handlers', () => {
    const slotClickHandler = vi.fn();
    const buttonClickHandler = vi.fn();
    
    render(
      <Slot onClick={slotClickHandler}>
        <button onClick={buttonClickHandler}>Click me</button>
      </Slot>
    );
    
    fireEvent.click(screen.getByRole('button'));
    
    expect(slotClickHandler).toHaveBeenCalledTimes(1);
    expect(buttonClickHandler).toHaveBeenCalledTimes(1);
  });
  
  it('should forward refs', () => {
    const ref = React.createRef<HTMLButtonElement>();
    
    render(
      <Slot ref={ref}>
        <button>Click me</button>
      </Slot>
    );
    
    expect(ref.current).not.toBeNull();
    expect(ref.current?.tagName).toBe('BUTTON');
  });
  
  it('should handle Slottable components', () => {
    render(
      <div>
        <Slot>
          <div>Before</div>
          <Slottable>
            <button data-testid="slotted-button">Slotted Button</button>
          </Slottable>
          <div>After</div>
        </Slot>
      </div>
    );
    
    const button = screen.getByTestId('slotted-button');
    expect(button).toBeInTheDocument();
    expect(button.textContent).toBe('Slotted Button');
    
    // Check that the surrounding content is preserved
    expect(screen.getByText('Before')).toBeInTheDocument();
    expect(screen.getByText('After')).toBeInTheDocument();
  });
  
  it('should handle null children', () => {
    // This should not throw
    expect(() => {
      render(
        <Slot>
          {null}
        </Slot>
      );
    }).not.toThrow();
  });
});
