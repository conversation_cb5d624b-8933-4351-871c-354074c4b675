import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { Slot, Slottable } from './slot';

const meta: Meta<typeof Slot> = {
  title: 'Utility/Slot',
  component: Slot,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    children: {
      control: 'text',
      description: 'The content to render',
    },
    className: {
      control: 'text',
      description: 'CSS class to apply to the element',
    },
    style: {
      control: 'object',
      description: 'Inline styles to apply to the element',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Slot>;

export const Default: Story = {
  args: {
    children: <button>Button inside a slot</button>,
  },
  parameters: {
    docs: {
      description: {
        story: 'The Slot component renders its children and passes props to them.',
      },
    },
  },
};

export const WithProps: Story = {
  args: {
    className: 'custom-class',
    style: { padding: '1rem', background: '#f0f0f0', borderRadius: '4px' },
    onClick: () => alert('Slot clicked'),
    children: <button>But<PERSON> with props from slot</button>,
  },
  parameters: {
    docs: {
      description: {
        story: 'Props passed to the Slot component are forwarded to its children.',
      },
    },
  },
};

export const MergingProps: Story = {
  render: () => (
    <Slot 
      className="slot-class" 
      style={{ color: 'blue', padding: '0.5rem' }}
      onClick={() => console.log('Slot clicked')}
    >
      <button 
        className="button-class" 
        style={{ backgroundColor: 'white', borderRadius: '4px' }}
        onClick={() => console.log('Button clicked')}
      >
        Button with merged props
      </button>
    </Slot>
  ),
  parameters: {
    docs: {
      description: {
        story: 'The Slot component merges className, style, and event handlers with its children.',
      },
    },
  },
};

export const WithSlottable: Story = {
  render: () => (
    <div>
      <p>Before</p>
      <Slot>
        <Slottable>
          <button data-testid="slotted-button">Slotted Button</button>
        </Slottable>
      </Slot>
      <p>After</p>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'The Slottable component allows for more complex composition patterns.',
      },
    },
  },
};
