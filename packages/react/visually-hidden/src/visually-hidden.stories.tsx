import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { VisuallyHidden, VISUALLY_HIDDEN_STYLES } from './visually-hidden';

const meta: Meta<typeof VisuallyHidden> = {
  title: 'Utility/VisuallyHidden',
  component: VisuallyHidden,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    children: {
      control: 'text',
      description: 'The content to be visually hidden but still accessible to screen readers',
    },
    style: {
      control: 'object',
      description: 'Additional styles to apply to the component',
    },
  },
};

export default meta;
type Story = StoryObj<typeof VisuallyHidden>;

export const Default: Story = {
  args: {
    children: 'This text is visually hidden but accessible to screen readers',
  },
  parameters: {
    docs: {
      description: {
        story: 'The VisuallyHidden component hides content visually while keeping it accessible to screen readers.',
      },
    },
  },
};

export const WithIcon: Story = {
  render: () => (
    <button
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '0.5rem',
        background: '#f0f0f0',
        border: '1px solid #ccc',
        borderRadius: '4px',
      }}
    >
      <VisuallyHidden>Close</VisuallyHidden>
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.5 3.5L3.5 12.5M3.5 3.5L12.5 12.5"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </button>
  ),
  parameters: {
    docs: {
      description: {
        story: 'A common use case for VisuallyHidden is to provide accessible text for icon buttons.',
      },
    },
  },
};

export const WithCustomStyles: Story = {
  args: {
    children: 'This text has custom styles in addition to being visually hidden',
    style: { color: 'red', fontWeight: 'bold' },
  },
  parameters: {
    docs: {
      description: {
        story: 'You can add custom styles to the VisuallyHidden component, which will be merged with the default visually hidden styles.',
      },
    },
  },
};

export const UsingStyles: Story = {
  render: () => (
    <div>
      <p>
        You can also use the <code>VISUALLY_HIDDEN_STYLES</code> object directly:
      </p>
      <pre style={{ background: '#f5f5f5', padding: '1rem', borderRadius: '4px' }}>
        {JSON.stringify(VISUALLY_HIDDEN_STYLES, null, 2)}
      </pre>
      <span style={{ ...VISUALLY_HIDDEN_STYLES }}>
        This text uses the VISUALLY_HIDDEN_STYLES object directly
      </span>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'The VISUALLY_HIDDEN_STYLES object can be imported and used directly with any element.',
      },
    },
  },
};
