import React from 'react';
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { VisuallyHidden, VISUALLY_HIDDEN_STYLES } from '../visually-hidden';

describe('VisuallyHidden', () => {
  it('should render children', () => {
    render(<VisuallyHidden>Hidden text</VisuallyHidden>);
    
    expect(screen.getByText('Hidden text')).toBeInTheDocument();
  });
  
  it('should apply visually hidden styles', () => {
    render(<VisuallyHidden data-testid="hidden">Hidden text</VisuallyHidden>);
    
    const element = screen.getByTestId('hidden');
    const styles = window.getComputedStyle(element);
    
    // Check a few key styles
    expect(styles.position).toBe('absolute');
    expect(styles.clip).toBe('rect(0px, 0px, 0px, 0px)');
    expect(styles.height).toBe('1px');
    expect(styles.width).toBe('1px');
    expect(styles.overflow).toBe('hidden');
  });
  
  it('should merge custom styles with visually hidden styles', () => {
    render(
      <VisuallyHidden data-testid="hidden" style={{ color: 'red', fontWeight: 'bold' }}>
        Hidden text
      </VisuallyHidden>
    );
    
    const element = screen.getByTestId('hidden');
    const styles = window.getComputedStyle(element);
    
    // Should have visually hidden styles
    expect(styles.position).toBe('absolute');
    expect(styles.height).toBe('1px');
    
    // Should also have custom styles
    expect(styles.color).toBe('red');
    expect(styles.fontWeight).toBe('bold');
  });
  
  it('should forward refs', () => {
    const ref = React.createRef<HTMLSpanElement>();
    
    render(<VisuallyHidden ref={ref}>Hidden text</VisuallyHidden>);
    
    expect(ref.current).not.toBeNull();
    expect(ref.current?.tagName).toBe('SPAN');
  });
  
  it('should pass additional props to the underlying element', () => {
    render(
      <VisuallyHidden data-testid="hidden" className="custom-class" aria-label="Hidden content">
        Hidden text
      </VisuallyHidden>
    );
    
    const element = screen.getByTestId('hidden');
    
    expect(element).toHaveClass('custom-class');
    expect(element).toHaveAttribute('aria-label', 'Hidden content');
  });
  
  it('should export VISUALLY_HIDDEN_STYLES for direct use', () => {
    expect(VISUALLY_HIDDEN_STYLES).toEqual(expect.objectContaining({
      position: 'absolute',
      clip: 'rect(0, 0, 0, 0)',
      height: 1,
      width: 1,
      overflow: 'hidden',
    }));
  });
});
