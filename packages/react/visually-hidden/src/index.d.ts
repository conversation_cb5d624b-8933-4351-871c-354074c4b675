import * as React from 'react';

/**
 * CSS styles to visually hide an element while keeping it accessible to screen readers
 */
export declare const VISUALLY_HIDDEN_STYLES: React.CSSProperties;

export interface VisuallyHiddenProps extends React.HTMLAttributes<HTMLSpanElement> {
  style?: React.CSSProperties;
}

/**
 * A component that visually hides content while keeping it accessible to screen readers
 */
export declare const VisuallyHidden: React.ForwardRefExoticComponent<VisuallyHiddenProps & React.RefAttributes<HTMLSpanElement>>;

/**
 * Alias for VisuallyHidden
 */
export declare const Root: React.ForwardRefExoticComponent<VisuallyHiddenProps & React.RefAttributes<HTMLSpanElement>>;
