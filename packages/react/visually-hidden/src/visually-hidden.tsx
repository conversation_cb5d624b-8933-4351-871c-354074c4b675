import * as React from 'react';
import { Primitive } from '@lasensoft/react-primitive';

/* -------------------------------------------------------------------------------------------------
 * VisuallyHidden
 * -----------------------------------------------------------------------------------------------*/

/**
 * CSS styles to visually hide an element while keeping it accessible to screen readers
 * See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss
 */
export const VISUALLY_HIDDEN_STYLES = Object.freeze({
  position: 'absolute',
  border: 0,
  width: 1,
  height: 1,
  padding: 0,
  margin: -1,
  overflow: 'hidden',
  clip: 'rect(0, 0, 0, 0)',
  whiteSpace: 'nowrap',
  wordWrap: 'normal',
}) satisfies React.CSSProperties;

const NAME = 'VisuallyHidden';

type VisuallyHiddenElement = React.ComponentRef<typeof Primitive.span>;
type PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;
export interface VisuallyHiddenProps extends PrimitiveSpanProps {
  style?: React.CSSProperties;
}

/**
 * A component that visually hides content while keeping it accessible to screen readers.
 *
 * @example
 * ```tsx
 * <button>
 *   <VisuallyHidden>Close</VisuallyHidden>
 *   <XIcon />
 * </button>
 * ```
 */
export const VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(
  (props, forwardedRef) => {
    return (
      <Primitive.span
        {...props}
        ref={forwardedRef}
        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}
      />
    );
  }
);

VisuallyHidden.displayName = NAME;

/* -----------------------------------------------------------------------------------------------*/

export const Root = VisuallyHidden;
