# @lasensoft/react-visually-hidden

A component that visually hides content while keeping it accessible to screen readers.

## Installation

```bash
npm install @lasensoft/react-visually-hidden
```

## Usage

```tsx
import { VisuallyHidden } from '@lasensoft/react-visually-hidden';

// Basic usage - hide text visually but keep it accessible to screen readers
function IconButton() {
  return (
    <button>
      <VisuallyHidden>Close</VisuallyHidden>
      <svg>...</svg>
    </button>
  );
}

// Use with custom styles
<VisuallyHidden style={{ color: 'red' }}>
  This text is hidden visually but accessible to screen readers
</VisuallyHidden>
```

## API

### VisuallyHidden

A component that visually hides content while keeping it accessible to screen readers.

#### Props

All HTML attributes for a `span` element are accepted.

### VISUALLY_HIDDEN_STYLES

The CSS styles used to visually hide content. You can use these styles directly in your own components if needed.

```tsx
import { VISUALLY_HIDDEN_STYLES } from '@lasensoft/react-visually-hidden';

function CustomHiddenElement() {
  return (
    <div style={VISUALLY_HIDDEN_STYLES}>
      Hidden content
    </div>
  );
}
```
