# @lasensoft/react-hooks

A collection of React hooks for building UI components.

## Installation

```bash
npm install @lasensoft/react-hooks
```

## Usage

```tsx
import { useLayoutEffect } from '@lasensoft/react-hooks';

function MyComponent() {
  const ref = React.useRef(null);
  
  useLayoutEffect(() => {
    // This runs synchronously after all DOM mutations
    // but won't cause warnings during SSR
    const rect = ref.current.getBoundingClientRect();
    // ...
  }, []);
  
  return <div ref={ref}>Content</div>;
}
```

## Available Hooks

### useLayoutEffect

A version of React's `useLayoutEffect` that doesn't cause warnings during server-side rendering.

```tsx
import { useLayoutEffect } from '@lasensoft/react-hooks';

function MyComponent() {
  useLayoutEffect(() => {
    // This runs synchronously after all DOM mutations
    // but won't cause warnings during SSR
    // ...
    return () => {
      // Cleanup function
    };
  }, [dependency1, dependency2]);
  
  return <div>Content</div>;
}
```
