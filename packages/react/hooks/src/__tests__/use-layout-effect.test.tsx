import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render } from '@testing-library/react';
import { useLayoutEffect } from '../use-layout-effect';

describe('useLayoutEffect', () => {
  const originalDocument = global.document;
  
  afterEach(() => {
    // Restore the original document after each test
    global.document = originalDocument;
  });
  
  it('should use React.useLayoutEffect when document is defined', () => {
    // Mock React.useLayoutEffect
    const mockUseLayoutEffect = vi.spyOn(React, 'useLayoutEffect');
    mockUseLayoutEffect.mockImplementation((fn) => fn());
    
    // Create a test component that uses our hook
    const TestComponent = () => {
      const callback = vi.fn();
      useLayoutEffect(callback);
      return null;
    };
    
    // Render the component
    render(<TestComponent />);
    
    // Verify that React.useLayoutEffect was called
    expect(mockUseLayoutEffect).toHaveBeenCalled();
    
    // Clean up
    mockUseLayoutEffect.mockRestore();
  });
  
  it('should use a noop function when document is not defined', () => {
    // Mock React.useLayoutEffect
    const mockUseLayoutEffect = vi.spyOn(React, 'useLayoutEffect');
    
    // Mock document as undefined
    delete (global as any).document;
    
    // Create a test component that uses our hook
    const TestComponent = () => {
      const callback = vi.fn();
      useLayoutEffect(callback);
      return null;
    };
    
    // Render the component
    render(<TestComponent />);
    
    // Verify that React.useLayoutEffect was not called
    expect(mockUseLayoutEffect).not.toHaveBeenCalled();
    
    // Clean up
    mockUseLayoutEffect.mockRestore();
  });
});
