import * as React from 'react';

/**
 * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.
 * This is because neither `useLayoutEffect` nor `useEffect` run on the server.
 * We use this safe version which suppresses the warning by replacing it with a noop on the server.
 *
 * @example
 * ```tsx
 * import { useLayoutEffect } from '@lasensoft/react-hooks';
 * 
 * function MyComponent() {
 *   useLayoutEffect(() => {
 *     // This runs synchronously after all DOM mutations
 *     // but won't cause warnings during SSR
 *     const rect = myRef.current.getBoundingClientRect();
 *     // ...
 *   }, []);
 *   
 *   return <div ref={myRef}>Content</div>;
 * }
 * ```
 * 
 * @see https://reactjs.org/docs/hooks-reference.html#uselayouteffect
 */
const useLayoutEffect = globalThis?.document ? React.useLayoutEffect : () => {};

export { useLayoutEffect };
