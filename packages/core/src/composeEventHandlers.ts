/**
 * Composes multiple event handlers into a single function.
 * Handlers are called in the order they are provided.
 * If any handler calls `event.preventDefault()`, subsequent handlers will still be called.
 *
 * @param originalHandler - The first event handler to call
 * @param nextHandler - The next event handler to call
 * @returns A function that calls both handlers in sequence
 */
export function composeEventHandlers<E extends React.SyntheticEvent | Event>(
  originalHandler: ((event: E) => void) | undefined,
  nextHandler: (event: E) => void
) {
  return (event: E) => {
    originalHandler?.(event);
    
    // Check if event.preventDefault was called
    if (!event.defaultPrevented) {
      nextHandler(event);
    }
  };
}
