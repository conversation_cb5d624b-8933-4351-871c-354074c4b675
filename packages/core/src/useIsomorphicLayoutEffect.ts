import * as React from 'react';

/**
 * A hook that resolves to useLayoutEffect when in the browser and useEffect when on the server.
 * This is useful for effects that need to run synchronously after all DOM mutations,
 * but would cause warnings when run during server-side rendering.
 */
export const useIsomorphicLayoutEffect =
  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;
