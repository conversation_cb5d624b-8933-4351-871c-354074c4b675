import * as React from 'react';

let serverHandoffComplete = false;
let idCounter = 0;

/**
 * A hook that generates a unique ID that works in both client and server environments.
 * Uses React's built-in useId when available (React 18+), otherwise falls back to a custom implementation.
 *
 * @param idOverride - Optional ID override
 * @returns A unique ID string
 */
export function useId(idOverride?: string): string {
  // If an ID was explicitly provided, use that
  if (idOverride !== undefined) return idOverride;

  // Use React's built-in useId if available (React 18+)
  if (React.useId !== undefined) {
    return React.useId();
  }

  // Fall back to custom implementation for React 17 and below
  const [id, setId] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (serverHandoffComplete === false) {
      serverHandoffComplete = true;
    }
  }, []);

  React.useEffect(() => {
    if (id === null) {
      setId(String(++idCounter));
    }
  }, [id]);

  return id ?? '';
}
