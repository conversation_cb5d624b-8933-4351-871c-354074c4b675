import * as React from 'react';

type CreateContextOptions<T> = {
  /**
   * The display name of the context
   */
  name: string;
  /**
   * The error message to throw if the context is accessed before it is provided
   */
  errorMessage?: string;
  /**
   * The default value of the context
   */
  defaultValue?: T;
};

/**
 * Creates a context with a display name and optional error message.
 * Provides a hook that throws an error if used outside of a provider.
 * 
 * @example
 * ```tsx
 * const [DialogProvider, useDialogContext] = createContext<DialogContextValue>({
 *   name: 'DialogContext',
 *   errorMessage: 'useDialogContext must be used within a DialogProvider',
 * });
 * ```
 */
export function createContext<T>(options: CreateContextOptions<T>) {
  const {
    name,
    errorMessage = `${name} context is undefined. Make sure you are using ${name}Provider`,
    defaultValue,
  } = options;

  const Context = React.createContext<T | undefined>(defaultValue);
  Context.displayName = name;

  /**
   * Hook that returns the context value and throws an error if used outside of a provider.
   */
  const useContext = () => {
    const context = React.useContext(Context);
    if (context === undefined) {
      throw new Error(errorMessage);
    }
    return context;
  };

  return [Context.Provider, useContext, Context] as const;
}
