import * as React from 'react';

type UseControllableStateParams<T> = {
  /**
   * The controlled value
   */
  value?: T;
  /**
   * The initial value to use when uncontrolled
   */
  defaultValue?: T;
  /**
   * Callback for when the value changes
   */
  onChange?: (value: T) => void;
  /**
   * The name of the component using this hook (for warning messages)
   */
  componentName?: string;
};

/**
 * A hook that manages state that can be either controlled or uncontrolled.
 *
 * @example
 * ```tsx
 * const [value, setValue] = useControllableState({
 *   value: props.value,
 *   defaultValue: props.defaultValue,
 *   onChange: props.onChange,
 *   componentName: 'Checkbox',
 * });
 * ```
 */
export function useControllableState<T>({
  value,
  defaultValue,
  onChange,
  componentName = 'Component',
}: UseControllableStateParams<T>) {
  const isControlled = value !== undefined;
  const [uncontrolledValue, setUncontrolledValue] = React.useState<T | undefined>(defaultValue);
  const prevIsControlledRef = React.useRef(isControlled);
  const lastValueRef = React.useRef<T | undefined>(value ?? defaultValue);

  // Warn if controlled/uncontrolled state changes
  React.useEffect(() => {
    if (
      prevIsControlledRef.current !== isControlled &&
      prevIsControlledRef.current !== undefined
    ) {
      // Only log in development
      if (typeof window !== 'undefined' && window.console) {
        console.warn(
          `${componentName} changed from ${
            prevIsControlledRef.current ? 'controlled' : 'uncontrolled'
          } to ${isControlled ? 'controlled' : 'uncontrolled'}.`
        );
      }
    }
    prevIsControlledRef.current = isControlled;
    lastValueRef.current = value;
  }, [componentName, isControlled, value]);

  const currentValue = isControlled ? value : uncontrolledValue;

  const setValue = React.useCallback(
    (nextValue: React.SetStateAction<T | undefined>) => {
      const resolvedNextValue =
        typeof nextValue === 'function'
          ? (nextValue as (prev: T | undefined) => T)(lastValueRef.current)
          : nextValue;

      if (!isControlled) {
        setUncontrolledValue(resolvedNextValue);
      }

      lastValueRef.current = resolvedNextValue;
      onChange?.(resolvedNextValue as T);
    },
    [isControlled, onChange]
  );

  return [currentValue, setValue] as const;
}
