# Lasensoft Primitives

A collection of low-level UI components for building high-quality design systems and web applications.

## Features
- Accessible and WAI-ARIA compliant
- Fully customizable and themeable
- Comprehensive keyboard navigation
- Thoroughly tested
- TypeScript-first development experience

## Installation

```bash
# With npm
npm install @lasensoft/primitives

# With yarn
yarn add @lasensoft/primitives

# With pnpm
pnpm add @lasensoft/primitives
```

## Documentation
See our documentation site for detailed usage examples and API references.

## License
MIT
