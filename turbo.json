{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "lint": {"outputs": []}, "lint:fix": {"outputs": []}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}, "typecheck": {"dependsOn": ["^typecheck"]}, "test": {"dependsOn": ["build"], "outputs": []}, "test:watch": {"cache": false}}, "globalEnv": ["NODE_ENV"]}