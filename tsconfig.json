{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "bundler", "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@lasensoft/core": ["packages/core/src"], "@lasensoft/theme": ["packages/theme/src"], "@lasensoft/react/*": ["packages/react/*/src"], "@lasensoft/react-primitive": ["packages/react/atoms/primitive/src"], "@lasensoft/react-button": ["packages/react/atoms/button/src"], "@lasensoft/react-label": ["packages/react/atoms/label/src"], "@lasensoft/react-slot": ["packages/react/slot/src"], "@lasensoft/react-utils": ["packages/react/utils/src"], "@lasensoft/react-hooks": ["packages/react/hooks/src"], "@lasensoft/react-portal": ["packages/react/portal/src"], "@lasensoft/react-visually-hidden": ["packages/react/visually-hidden/src"], "@internal/tsconfig/*": ["internal/tsconfig/*"]}}, "exclude": ["node_modules", "dist"]}