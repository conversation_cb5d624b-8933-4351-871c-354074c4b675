#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Generates a package.json file with consistent structure
 * @param {Object} options - Options for generating the package.json
 * @param {string} options.packageName - The name of the package
 * @param {string} options.packagePath - The path to the package
 * @param {string[]} options.dependencies - Additional dependencies to include
 * @param {Object} options.additionalFields - Additional fields to include in the package.json
 */
function generatePackageJson({
  packageName,
  packagePath,
  dependencies = [],
  additionalFields = {},
}) {
  // Common dependencies for all packages
  const commonDependencies = {
    '@lasensoft/core': 'workspace:*',
  };

  // Add additional dependencies
  const allDependencies = {
    ...commonDependencies,
    ...dependencies.reduce((acc, dep) => {
      acc[dep] = 'workspace:*';
      return acc;
    }, {}),
  };

  // Common devDependencies for all packages
  const devDependencies = {
    '@internal/tsconfig': 'workspace:*',
    '@types/react': '^19.1.4',
    '@types/react-dom': '^19.1.5',
    'react': '^19.1.0',
    'react-dom': '^19.1.0',
    'typescript': '^5.8.3',
    'vite': '^6.3.5',
    'vitest': '^3.1.3',
  };

  // Common peerDependencies for all packages
  const peerDependencies = {
    'react': '^16.8 || ^17.0 || ^18.0',
    'react-dom': '^16.8 || ^17.0 || ^18.0',
  };

  // Base package.json structure
  const packageJson = {
    name: packageName,
    version: '0.1.0',
    license: 'MIT',
    source: './src/index.ts',
    main: './dist/index.js',
    module: './dist/index.mjs',
    types: './dist/index.d.ts',
    files: [
      'dist',
      'README.md',
    ],
    scripts: {
      build: 'vite build',
      lint: 'eslint src',
      'lint:fix': 'eslint --fix src',
      clean: 'rm -rf dist',
      typecheck: 'tsc --noEmit',
      test: 'vitest run',
      'test:watch': 'vitest',
    },
    dependencies: allDependencies,
    peerDependencies,
    devDependencies,
    ...additionalFields,
  };

  // Write the package.json file
  fs.writeFileSync(
    path.join(packagePath, 'package.json'),
    JSON.stringify(packageJson, null, 2) + '\n'
  );

  console.log(`Generated package.json for ${packageName}`);
}

// If this script is run directly, parse command line arguments
if (require.main === module) {
  const args = process.argv.slice(2);
  const packageName = args[0];
  const packagePath = args[1];
  const dependencies = args.slice(2);

  if (!packageName || !packagePath) {
    console.error('Usage: generate-package-json.js <packageName> <packagePath> [dependencies...]');
    process.exit(1);
  }

  generatePackageJson({
    packageName,
    packagePath,
    dependencies,
  });
}

module.exports = generatePackageJson;
