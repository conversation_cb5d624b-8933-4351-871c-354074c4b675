#!/usr/bin/env node

/**
 * This script helps manage TypeScript configurations across the project.
 * It allows you to use package paths in your tsconfig.json files and
 * generates the necessary vitest.config.ts files for testing.
 *
 * Usage:
 * node scripts/setup-tsconfig.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Root directory of the project
const rootDir = path.resolve(__dirname, '..');

// Find all tsconfig.json files in the project
function findTsConfigFiles() {
  const result = execSync('find . -name "tsconfig.json" -not -path "*/node_modules/*"', {
    cwd: rootDir,
    encoding: 'utf-8'
  });

  return result.trim().split('\n').filter(Boolean);
}

// Generate a vitest.config.ts file for a package
function generateVitestConfig(packageDir) {
  const relativePath = path.relative(rootDir, packageDir);
  const depth = relativePath.split(path.sep).length - 1;
  const rootRelativePath = '../'.repeat(depth);

  // Determine package dependencies
  const packageJsonPath = path.join(packageDir, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    return;
  }

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
  const dependencies = {
    ...packageJson.dependencies || {},
    ...packageJson.peerDependencies || {}
  };

  // Generate aliases for workspace dependencies
  const aliases = Object.keys(dependencies)
    .filter(dep => dep.startsWith('@lasensoft/'))
    .reduce((acc, dep) => {
      if (dep === '@lasensoft/core') {
        acc[dep] = `resolve(__dirname, '${rootRelativePath}packages/core/src')`;
      } else if (dep.startsWith('@lasensoft/react-')) {
        const componentName = dep.replace('@lasensoft/react-', '');
        // Handle different directory structures
        if (['primitive', 'button', 'label'].includes(componentName)) {
          acc[dep] = `resolve(__dirname, '${rootRelativePath}packages/react/atoms/${componentName}/src')`;
        } else if (['slot', 'portal', 'visually-hidden'].includes(componentName)) {
          acc[dep] = `resolve(__dirname, '${rootRelativePath}packages/react/${componentName}/src')`;
        } else if (componentName === 'utils' || componentName === 'hooks') {
          acc[dep] = `resolve(__dirname, '${rootRelativePath}packages/react/${componentName}/src')`;
        }
      }
      return acc;
    }, {});

  // Only generate vitest config if there are workspace dependencies
  if (Object.keys(aliases).length === 0) {
    return;
  }

  const aliasesString = Object.entries(aliases)
    .map(([key, value]) => `      '${key}': ${value},`)
    .join('\n');

  const vitestConfig = `import { defineConfig } from 'vitest/config';
import { resolve } from 'path';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['${rootRelativePath}internal/test-utils/src/setup.ts'],
  },
  resolve: {
    alias: {
${aliasesString}
    },
  },
});`;

  const vitestConfigPath = path.join(packageDir, 'vitest.config.ts');
  fs.writeFileSync(vitestConfigPath, vitestConfig);
  console.log(`Generated vitest.config.ts for ${relativePath}`);
}

// Main function
function main() {
  const tsConfigFiles = findTsConfigFiles();

  for (const tsConfigFile of tsConfigFiles) {
    const packageDir = path.dirname(path.join(rootDir, tsConfigFile));

    // Generate vitest.config.ts for packages with tests
    const testsDir = path.join(packageDir, 'src', '__tests__');

    // Also check if the package has a package.json with dependencies
    const packageJsonPath = path.join(packageDir, 'package.json');
    const hasPackageJson = fs.existsSync(packageJsonPath);

    if (fs.existsSync(testsDir) || hasPackageJson) {
      generateVitestConfig(packageDir);
    }
  }

  console.log('TypeScript configuration setup complete!');
}

main();
