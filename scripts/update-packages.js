#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const generatePackageJson = require('./generate-package-json');

// Root directory of the project
const rootDir = path.resolve(__dirname, '..');

// Packages to update
const packages = [
  {
    name: '@lasensoft/core',
    path: path.join(rootDir, 'packages/core'),
    dependencies: [],
  },
  {
    name: '@lasensoft/react-primitive',
    path: path.join(rootDir, 'packages/react/atoms/primitive'),
    dependencies: ['@lasensoft/react-slot', '@lasensoft/react-utils'],
  },
  {
    name: '@lasensoft/react-button',
    path: path.join(rootDir, 'packages/react/atoms/button'),
    dependencies: ['@lasensoft/react-primitive', '@lasensoft/react-utils'],
    additionalFields: {
      dependencies: {
        'clsx': '^2.1.1',
      },
    },
  },
  {
    name: '@lasensoft/react-label',
    path: path.join(rootDir, 'packages/react/atoms/label'),
    dependencies: ['@lasensoft/react-primitive', '@lasensoft/react-utils'],
  },
  {
    name: '@lasensoft/react-utils',
    path: path.join(rootDir, 'packages/react/utils'),
    dependencies: [],
  },
  {
    name: '@lasensoft/react-slot',
    path: path.join(rootDir, 'packages/react/slot'),
    dependencies: ['@lasensoft/react-utils'],
  },
  {
    name: '@lasensoft/react-hooks',
    path: path.join(rootDir, 'packages/react/hooks'),
    dependencies: [],
  },
  {
    name: '@lasensoft/react-portal',
    path: path.join(rootDir, 'packages/react/portal'),
    dependencies: ['@lasensoft/react-hooks'],
  },
  {
    name: '@lasensoft/react-visually-hidden',
    path: path.join(rootDir, 'packages/react/visually-hidden'),
    dependencies: [],
  },
];

// Update vite.config.ts for each package
function updateViteConfig(packageInfo) {
  const viteConfigPath = path.join(packageInfo.path, 'vite.config.ts');
  
  // Skip if the file doesn't exist
  if (!fs.existsSync(viteConfigPath)) {
    return;
  }
  
  // Generate the new vite.config.ts content
  const packageNameParts = packageInfo.name.split('/');
  const shortName = packageNameParts[packageNameParts.length - 1];
  const globalName = `Lasensoft${shortName.charAt(0).toUpperCase() + shortName.slice(1).replace(/-./g, x => x[1].toUpperCase())}`;
  
  // Additional externals and globals
  const additionalExternals = [];
  const additionalGlobals = {};
  
  // Add clsx if it's a dependency
  if (packageInfo.additionalFields?.dependencies?.clsx) {
    additionalExternals.push('clsx');
    additionalGlobals.clsx = 'clsx';
  }
  
  // Add all dependencies to externals and globals
  packageInfo.dependencies.forEach(dep => {
    const depParts = dep.split('/');
    const depShortName = depParts[depParts.length - 1];
    const depGlobalName = `Lasensoft${depShortName.charAt(0).toUpperCase() + depShortName.slice(1).replace(/-./g, x => x[1].toUpperCase())}`;
    
    additionalExternals.push(dep);
    additionalGlobals[dep] = depGlobalName;
  });
  
  const relativePath = path.relative(packageInfo.path, rootDir).replace(/\\/g, '/');
  
  const content = `import { createPackageConfig } from '${relativePath}/internal/build/vite-config';

export default createPackageConfig({
  packageName: '${packageInfo.name}',
  globalName: '${globalName}',
  ${additionalExternals.length > 0 ? `additionalExternals: ${JSON.stringify(additionalExternals)},` : ''}
  ${Object.keys(additionalGlobals).length > 0 ? `additionalGlobals: ${JSON.stringify(additionalGlobals, null, 2)},` : ''}
  dtsOptions: {
    tsconfigPath: './tsconfig.json'
  }
});`;
  
  fs.writeFileSync(viteConfigPath, content);
  console.log(`Updated vite.config.ts for ${packageInfo.name}`);
}

// Update vitest.config.ts for each package
function updateVitestConfig(packageInfo) {
  const vitestConfigPath = path.join(packageInfo.path, 'vitest.config.ts');
  
  // Skip if the file doesn't exist
  if (!fs.existsSync(vitestConfigPath)) {
    return;
  }
  
  const relativePath = path.relative(packageInfo.path, rootDir).replace(/\\/g, '/');
  
  const content = `import { createTestConfig } from '${relativePath}/internal/build/vite-config';

export default createTestConfig();`;
  
  fs.writeFileSync(vitestConfigPath, content);
  console.log(`Updated vitest.config.ts for ${packageInfo.name}`);
}

// Update tsconfig.json for each package
function updateTsConfig(packageInfo) {
  const tsConfigPath = path.join(packageInfo.path, 'tsconfig.json');
  
  // Skip if the file doesn't exist
  if (!fs.existsSync(tsConfigPath)) {
    return;
  }
  
  const relativePath = path.relative(packageInfo.path, rootDir).replace(/\\/g, '/');
  
  // Read the existing tsconfig.json
  const tsConfig = JSON.parse(fs.readFileSync(tsConfigPath, 'utf8'));
  
  // Update the extends field
  tsConfig.extends = [`${relativePath}/internal/tsconfig/react-library.json`];
  
  // Remove any references to tsconfig.paths.json
  if (Array.isArray(tsConfig.extends)) {
    tsConfig.extends = tsConfig.extends.filter(ext => !ext.includes('tsconfig.paths.json'));
  }
  
  // Write the updated tsconfig.json
  fs.writeFileSync(tsConfigPath, JSON.stringify(tsConfig, null, 2) + '\n');
  console.log(`Updated tsconfig.json for ${packageInfo.name}`);
  
  // Remove tsconfig.paths.json if it exists
  const tsConfigPathsPath = path.join(packageInfo.path, 'tsconfig.paths.json');
  if (fs.existsSync(tsConfigPathsPath)) {
    fs.unlinkSync(tsConfigPathsPath);
    console.log(`Removed tsconfig.paths.json for ${packageInfo.name}`);
  }
}

// Update package.json for each package
function updatePackageJson(packageInfo) {
  generatePackageJson({
    packageName: packageInfo.name,
    packagePath: packageInfo.path,
    dependencies: packageInfo.dependencies,
    additionalFields: packageInfo.additionalFields || {},
  });
}

// Main function to update all packages
function updateAllPackages() {
  packages.forEach(packageInfo => {
    console.log(`Updating ${packageInfo.name}...`);
    
    // Create the package directory if it doesn't exist
    if (!fs.existsSync(packageInfo.path)) {
      fs.mkdirSync(packageInfo.path, { recursive: true });
      console.log(`Created directory for ${packageInfo.name}`);
    }
    
    // Update the package files
    updateViteConfig(packageInfo);
    updateVitestConfig(packageInfo);
    updateTsConfig(packageInfo);
    updatePackageJson(packageInfo);
    
    console.log(`Finished updating ${packageInfo.name}`);
  });
  
  console.log('All packages updated successfully!');
}

// Run the update
updateAllPackages();
