# Plan for Building @lasensoft/primitives from Scratch

## Overview
This plan outlines the steps to build a React UI primitives library from scratch, inspired by the base_primitives project but implemented component by component. The library will use the `@lasensoft/primitives` namespace.

## Project Goals
- Create a comprehensive, accessible UI component library
- Follow modern React best practices and patterns
- Ensure components are customizable, composable, and well-tested
- Build a solid foundation for design systems

## Phase 1: Project Setup and Infrastructure

### 1. Initialize Project Structure

#### 1.1 Create Project Directory and Initialize Git
```bash
# Create project directory
mkdir -p /home/<USER>/project/myreact-ui/primitives
cd /home/<USER>/project/myreact-ui/primitives

# Initialize git repository
git init

# Create initial .gitignore file
cat > .gitignore << EOF
# Dependencies
node_modules
.pnp
.pnp.js

# Build outputs
dist
build
.next
out

# Testing
coverage

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Storybook
storybook-static
EOF
```

#### 1.2 Create Initial Commit
```bash
# Add initial files and create first commit
git add .gitignore
git commit -m "Initial commit: Project setup"
```

### 2. Set Up Monorepo Structure

#### 2.1 Create Directory Structure
```bash
# Create main package directories
mkdir -p packages/core
mkdir -p packages/react

# Create application directories
mkdir -p apps/docs
mkdir -p apps/storybook

# Create supporting directories
mkdir -p internal/build
mkdir -p internal/test-utils
mkdir -p internal/tsconfig

# Create utility directories
mkdir -p scripts
mkdir -p .changeset
mkdir -p types
```

#### 2.2 Create README Files for Each Directory
```bash
# Create README files for main directories
cat > packages/README.md << EOF
# Packages

This directory contains the core packages of the @lasensoft/primitives library:

- **core**: Core utilities and shared functionality
- **react**: React UI components organized by component type
EOF

cat > apps/README.md << EOF
# Applications

This directory contains applications for development, testing, and documentation:

- **docs**: Documentation website
- **storybook**: Component playground and development environment
EOF

cat > internal/README.md << EOF
# Internal

This directory contains internal tooling and configurations:

- **build**: Build configurations and utilities
- **test-utils**: Testing utilities and helpers
- **tsconfig**: Shared TypeScript configurations
EOF
```

### 3. Configure Package Manager and Workspaces

#### 3.1 Set Up PNPM Workspace
```bash
# Create pnpm workspace configuration
cat > pnpm-workspace.yaml << EOF
packages:
  - 'packages/*'
  - 'apps/*'
  - 'internal/*'
EOF

# Create .npmrc file
cat > .npmrc << EOF
node-linker=hoisted
strict-peer-dependencies=false
auto-install-peers=true
EOF
```

#### 3.2 Create Root Package.json
```bash
# Create root package.json
cat > package.json << EOF
{
  "private": true,
  "name": "@lasensoft/primitives",
  "version": "0.1.0",
  "license": "MIT",
  "scripts": {
    "lint": "pnpm -r run lint",
    "lint:fix": "pnpm -r run lint:fix",
    "types:check": "pnpm -r --parallel run typecheck",
    "test": "vitest",
    "test:watch": "vitest watch",
    "test:coverage": "vitest run --coverage",
    "storybook": "pnpm --filter=@lasensoft/storybook run dev",
    "dev": "pnpm run storybook",
    "build": "pnpm -r --parallel --filter \"./packages/**/*\" run build",
    "build:storybook": "pnpm --filter=@lasensoft/storybook run build",
    "clean": "pnpm -r run clean",
    "reset": "pnpm run clean && rm -rf node_modules",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "changeset": "changeset",
    "version-packages": "changeset version",
    "release": "pnpm build && changeset publish"
  },
  "engines": {
    "node": ">=18"
  },
  "prettier": {
    "trailingComma": "es5",
    "printWidth": 100,
    "singleQuote": true,
    "tabWidth": 2,
    "semi": true
  },
  "devDependencies": {
    "@changesets/cli": "^2.26.0",
    "@testing-library/react": "^14.0.0",
    "@types/node": "^18.0.0",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "@typescript-eslint/eslint-plugin": "^5.59.0",
    "@typescript-eslint/parser": "^5.59.0",
    "@vitejs/plugin-react": "^4.0.0",
    "eslint": "^8.38.0",
    "eslint-plugin-react": "^7.32.2",
    "eslint-plugin-react-hooks": "^4.6.0",
    "prettier": "^2.8.7",
    "tsup": "^6.7.0",
    "typescript": "^5.0.4",
    "vite": "^4.3.1",
    "vitest": "^0.30.1"
  }
}
EOF
```

### 4. Set Up Core Configuration Files

#### 4.1 TypeScript Configuration
```bash
# Create base TypeScript configuration
cat > tsconfig.json << EOF
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "jsx": "react-jsx",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "isolatedModules": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true
  },
  "exclude": ["node_modules", "dist"]
}
EOF

# Create shared TypeScript configurations
mkdir -p internal/tsconfig
cat > internal/tsconfig/base.json << EOF
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "display": "Default",
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "jsx": "react-jsx",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  }
}
EOF

cat > internal/tsconfig/react-library.json << EOF
{
  "$schema": "https://json.schemastore.org/tsconfig",
  "display": "React Library",
  "extends": "./base.json",
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "isolatedModules": true
  }
}
EOF
```

#### 4.2 ESLint Configuration
```bash
# Create ESLint configuration
cat > eslint.config.mjs << EOF
export default {
  root: true,
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:@typescript-eslint/recommended'
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['react', 'react-hooks', '@typescript-eslint'],
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true
    }
  },
  settings: {
    react: {
      version: 'detect'
    }
  },
  rules: {
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
    '@typescript-eslint/no-unused-vars': ['warn', { 'argsIgnorePattern': '^_' }],
    '@typescript-eslint/explicit-module-boundary-types': 'off'
  }
};
EOF
```

#### 4.3 Prettier Configuration
```bash
# Create Prettier configuration
cat > .prettierrc << EOF
{
  "trailingComma": "es5",
  "printWidth": 100,
  "singleQuote": true,
  "tabWidth": 2,
  "semi": true
}
EOF

cat > .prettierignore << EOF
node_modules
dist
build
coverage
.next
out
storybook-static
pnpm-lock.yaml
EOF
```

#### 4.4 Vitest Configuration
```bash
# Create Vitest configuration
cat > vitest.config.mts << EOF
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./internal/test-utils/setup.ts'],
    coverage: {
      reporter: ['text', 'json', 'html']
    }
  },
  resolve: {
    alias: {
      '@lasensoft/core': resolve(__dirname, 'packages/core/src'),
      '@lasensoft/react': resolve(__dirname, 'packages/react')
    }
  }
});
EOF

# Create test setup file
mkdir -p internal/test-utils
cat > internal/test-utils/setup.ts << EOF
import '@testing-library/jest-dom';
EOF
```

### 5. Create Philosophy and Documentation

#### 5.1 Create Philosophy Document
```bash
# Create philosophy document
cat > philosophy.md << EOF
# Lasensoft Primitives: Philosophy and Guiding Principles

## Vision
Our goal is to create a well-designed, accessible component library that serves as building blocks for design systems. We aim to provide developers with a set of low-level UI components that are highly customizable, accessible by default, and follow best practices.

## Principles

### 1. Accessible
- Components adhere to WAI-ARIA guidelines and patterns
- Keyboard navigation is fully supported
- Focus management is handled appropriately
- Screen reader announcements are clear and helpful
- Color contrast meets WCAG standards

### 2. Functional
- Components are feature-rich with keyboard navigation support
- State management is handled efficiently
- Components respond appropriately to user interactions
- Edge cases are handled gracefully

### 3. Composable
- Components follow a 1-to-1 mapping with DOM elements
- Component APIs are designed for composition
- Props are forwarded appropriately
- Refs are handled correctly

### 4. Customizable
- Components ship with minimal styling for complete design freedom
- Styling hooks are provided for common customization needs
- Variants and states are exposed via data attributes
- Components can be extended and overridden

### 5. Interoperable
- Works well with various styling solutions (CSS, CSS-in-JS, utility classes)
- Compatible with different React frameworks (Next.js, Remix, etc.)
- Supports internationalization and right-to-left languages
- Provides both controlled and uncontrolled component variants

## Design Decisions

### Component API
- Components use a consistent API pattern
- Props are named intuitively and consistently
- Event handlers follow React conventions
- Components provide appropriate default behaviors

### State Management
- Components can be controlled or uncontrolled
- State transitions are predictable and documented
- Complex state is managed with reducers when appropriate
- Context is used judiciously for component composition

### Styling Approach
- Components use CSS variables for theming
- Styles are scoped to avoid conflicts
- CSS-in-JS is used for dynamic styles
- Utility classes can be applied for quick customization

### Testing Strategy
- Components are tested for functionality and accessibility
- Visual regression tests ensure consistent appearance
- User interactions are tested with realistic scenarios
- Edge cases are covered with specific tests
EOF
```

#### 5.2 Create README
```bash
# Create README
cat > README.md << EOF
# Lasensoft Primitives

A collection of low-level UI components for building high-quality design systems and web applications.

## Features
- Accessible and WAI-ARIA compliant
- Fully customizable and themeable
- Comprehensive keyboard navigation
- Thoroughly tested
- TypeScript-first development experience

## Installation

```bash
# With npm
npm install @lasensoft/primitives

# With yarn
yarn add @lasensoft/primitives

# With pnpm
pnpm add @lasensoft/primitives
```

## Usage

```jsx
import { Button } from '@lasensoft/primitives';

function App() {
  return (
    <Button variant="primary" onClick={() => console.log('Clicked!')}>
      Click me
    </Button>
  );
}
```

## Documentation
See our documentation site for detailed usage examples and API references.

## Components

### Form Components
- Button
- Checkbox
- RadioGroup
- Switch
- Slider

### Layout Components
- AspectRatio
- Separator
- ScrollArea

### Navigation Components
- Tabs
- Accordion

### Overlay Components
- Dialog
- Popover
- Tooltip

## Contributing
We welcome contributions! Please see our contributing guidelines for details.

## License
MIT
EOF
```

#### 5.3 Create Contributing Guidelines
```bash
# Create contributing guidelines
cat > CONTRIBUTING.md << EOF
# Contributing to Lasensoft Primitives

Thank you for your interest in contributing to Lasensoft Primitives! This document provides guidelines and instructions for contributing.

## Development Process

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## Setup Development Environment

```bash
# Clone the repository
git clone https://github.com/yourusername/primitives.git
cd primitives

# Install dependencies
pnpm install

# Start Storybook
pnpm dev
```

## Project Structure

- \`packages/core\`: Core utilities and shared functionality
- \`packages/react\`: React UI components
- \`apps/docs\`: Documentation website
- \`apps/storybook\`: Component playground

## Coding Standards

- Follow the existing code style
- Write tests for new features
- Ensure accessibility compliance
- Document component APIs

## Pull Request Process

1. Update documentation if needed
2. Add or update tests as necessary
3. Ensure all tests pass
4. Update the changelog with your changes
5. Submit the pull request

## Release Process

We use Changesets for versioning and publishing. When making changes:

1. Run \`pnpm changeset\` to create a changeset
2. Commit the changeset with your changes
3. When a PR is merged, the changeset will be used to determine version bumps

## Code of Conduct

Please follow our Code of Conduct in all your interactions with the project.
EOF
```

#### 5.4 Create License
```bash
# Create license file
cat > LICENSE << EOF
MIT License

Copyright (c) 2023 Lasensoft

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
EOF
```

## Phase 2: Core Utilities and Shared Functionality

### 1. Set Up Core Package

#### 1.1 Create Core Package Structure
```bash
# Create core package directory structure
mkdir -p packages/core/src
mkdir -p packages/core/tests

# Create core package README
cat > packages/core/README.md << EOF
# @lasensoft/core

Core utilities and shared functionality for the Lasensoft Primitives component library.

## Installation

```bash
npm install @lasensoft/core
```

## Usage

```tsx
import { composeEventHandlers } from '@lasensoft/core';

// Use utilities in your components
```

## Utilities

- **composeEventHandlers**: Compose multiple event handlers into one
- **createContext**: Create a React context with error handling
- **useControllableState**: Manage both controlled and uncontrolled component states
- **useId**: Generate unique IDs for accessibility
EOF
```

#### 1.2 Configure Core Package
```bash
# Create core package.json
cat > packages/core/package.json << EOF
{
  "name": "@lasensoft/core",
  "version": "0.1.0",
  "license": "MIT",
  "source": "./src/index.ts",
  "main": "./src/index.ts",
  "module": "./src/index.ts",
  "publishConfig": {
    "main": "./dist/index.js",
    "module": "./dist/index.mjs",
    "types": "./dist/index.d.ts"
  },
  "files": [
    "dist",
    "README.md"
  ],
  "scripts": {
    "lint": "eslint --max-warnings 0 src",
    "lint:fix": "eslint --fix src",
    "clean": "rm -rf dist",
    "typecheck": "tsc --noEmit",
    "test": "vitest run",
    "test:watch": "vitest",
    "build": "tsup src/index.ts --format esm,cjs --dts"
  },
  "peerDependencies": {
    "react": "^16.8 || ^17.0 || ^18.0"
  },
  "devDependencies": {
    "@lasensoft/test-utils": "workspace:*",
    "@types/react": "^18.0.0",
    "react": "^18.0.0",
    "tsup": "^6.7.0",
    "typescript": "^5.0.4",
    "vitest": "^0.30.1"
  }
}
EOF

# Create TypeScript configuration for core package
cat > packages/core/tsconfig.json << EOF
{
  "extends": "../../internal/tsconfig/react-library.json",
  "include": ["src"],
  "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.test.tsx"],
  "compilerOptions": {
    "outDir": "./dist"
  }
}
EOF
```

### 2. Implement Core Utilities

#### 2.1 Event Handler Composition
```bash
# Create primitive utility for event handler composition
cat > packages/core/src/compose-event-handlers.ts << EOF
/**
 * Composes multiple event handlers into a single event handler.
 * Useful for combining component event handlers with user-provided ones.
 */
export function composeEventHandlers<E>(
  originalEventHandler?: (event: E) => void,
  ourEventHandler?: (event: E) => void,
  { checkForDefaultPrevented = true } = {}
) {
  return function handleEvent(event: E) {
    // Call the original handler first
    originalEventHandler?.(event);

    // If the original handler called preventDefault() and we're checking for it,
    // don't call our handler
    if (checkForDefaultPrevented && (event as unknown as { defaultPrevented: boolean }).defaultPrevented) {
      return;
    }

    // Call our handler
    return ourEventHandler?.(event);
  };
}
EOF

# Create test for compose-event-handlers
mkdir -p packages/core/src/__tests__
cat > packages/core/src/__tests__/compose-event-handlers.test.ts << EOF
import { describe, it, expect, vi } from 'vitest';
import { composeEventHandlers } from '../compose-event-handlers';

describe('composeEventHandlers', () => {
  it('should call both handlers', () => {
    const originalHandler = vi.fn();
    const ourHandler = vi.fn();
    const composedHandler = composeEventHandlers(originalHandler, ourHandler);

    const event = { type: 'click' };
    composedHandler(event);

    expect(originalHandler).toHaveBeenCalledWith(event);
    expect(ourHandler).toHaveBeenCalledWith(event);
  });

  it('should not call our handler if original handler calls preventDefault()', () => {
    const originalHandler = vi.fn((event) => {
      event.preventDefault();
    });
    const ourHandler = vi.fn();
    const composedHandler = composeEventHandlers(originalHandler, ourHandler);

    const event = {
      type: 'click',
      preventDefault: vi.fn(),
      defaultPrevented: true
    };
    composedHandler(event);

    expect(originalHandler).toHaveBeenCalledWith(event);
    expect(ourHandler).not.toHaveBeenCalled();
  });

  it('should call our handler even if preventDefault() is called when checkForDefaultPrevented is false', () => {
    const originalHandler = vi.fn((event) => {
      event.preventDefault();
    });
    const ourHandler = vi.fn();
    const composedHandler = composeEventHandlers(
      originalHandler,
      ourHandler,
      { checkForDefaultPrevented: false }
    );

    const event = {
      type: 'click',
      preventDefault: vi.fn(),
      defaultPrevented: true
    };
    composedHandler(event);

    expect(originalHandler).toHaveBeenCalledWith(event);
    expect(ourHandler).toHaveBeenCalledWith(event);
  });
});
EOF
```

#### 2.2 Controllable State Hook
```bash
# Create controllable state hook
cat > packages/core/src/use-controllable-state.ts << EOF
import { useState, useCallback, useRef, useEffect } from 'react';

export interface UseControllableStateProps<T> {
  /**
   * The controlled value
   */
  prop?: T;

  /**
   * The default value when uncontrolled
   */
  defaultProp?: T;

  /**
   * Callback for when the value changes
   */
  onChange?: (value: T) => void;

  /**
   * Name of the component using this hook (for warnings)
   */
  componentName?: string;
}

/**
 * A hook that manages both controlled and uncontrolled component states.
 *
 * @example
 * ```tsx
 * const [value, setValue] = useControllableState({
 *   prop: controlledValue,
 *   defaultProp: defaultValue,
 *   onChange: onValueChange,
 * });
 * ```
 */
export function useControllableState<T>({
  prop,
  defaultProp,
  onChange,
  componentName = 'Component',
}: UseControllableStateProps<T>) {
  const [uncontrolledProp, setUncontrolledProp] = useState<T | undefined>(defaultProp);
  const isControlled = prop !== undefined;
  const value = isControlled ? prop : uncontrolledProp;
  const prevIsControlledRef = useRef(isControlled);

  // Issue a warning if the component switches from controlled to uncontrolled or vice versa
  useEffect(() => {
    if (process.env.NODE_ENV !== 'production') {
      if (prevIsControlledRef.current !== isControlled) {
        console.warn(
          \`\${componentName} is changing from \${
            prevIsControlledRef.current ? 'controlled' : 'uncontrolled'
          } to \${isControlled ? 'controlled' : 'uncontrolled'}.
          Components should not switch from controlled to uncontrolled (or vice versa).
          Use the 'value' prop with an onChange handler, or use an uncontrolled component with a defaultValue.\`
        );
      }
    }

    prevIsControlledRef.current = isControlled;
  }, [componentName, isControlled]);

  const setValue = useCallback(
    (nextValue: React.SetStateAction<T | undefined>) => {
      // If the component is controlled, don't update internal state
      if (!isControlled) {
        setUncontrolledProp(nextValue);
      }

      // Call onChange handler if provided
      if (onChange) {
        // Handle functional updates
        const setter = nextValue as (prevState?: T) => T;
        const resolvedNextValue =
          typeof nextValue === 'function' ? setter(value) : nextValue;

        if (resolvedNextValue !== value) {
          onChange(resolvedNextValue as T);
        }
      }
    },
    [isControlled, onChange, value]
  );

  return [value, setValue] as const;
}
EOF

# Create test for use-controllable-state
cat > packages/core/src/__tests__/use-controllable-state.test.tsx << EOF
import { describe, it, expect, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useControllableState } from '../use-controllable-state';

describe('useControllableState', () => {
  it('should use the controlled value when provided', () => {
    const { result } = renderHook(() =>
      useControllableState({ prop: 'controlled', defaultProp: 'default' })
    );

    expect(result.current[0]).toBe('controlled');
  });

  it('should use the default value when uncontrolled', () => {
    const { result } = renderHook(() =>
      useControllableState({ defaultProp: 'default' })
    );

    expect(result.current[0]).toBe('default');
  });

  it('should update the value when setValue is called in uncontrolled mode', () => {
    const { result } = renderHook(() =>
      useControllableState({ defaultProp: 'default' })
    );

    act(() => {
      result.current[1]('new value');
    });

    expect(result.current[0]).toBe('new value');
  });

  it('should call onChange when setValue is called', () => {
    const onChange = vi.fn();
    const { result } = renderHook(() =>
      useControllableState({ defaultProp: 'default', onChange })
    );

    act(() => {
      result.current[1]('new value');
    });

    expect(onChange).toHaveBeenCalledWith('new value');
  });

  it('should not update internal state when controlled', () => {
    const onChange = vi.fn();
    const { result } = renderHook(() =>
      useControllableState({ prop: 'controlled', onChange })
    );

    act(() => {
      result.current[1]('new value');
    });

    // Value should still be the controlled value
    expect(result.current[0]).toBe('controlled');
    // But onChange should have been called
    expect(onChange).toHaveBeenCalledWith('new value');
  });
});
EOF
```

#### 2.3 Create Context Utility
```bash
# Create context utility
cat > packages/core/src/create-context.ts << EOF
import React, { createContext as createReactContext, useContext } from 'react';

/**
 * Creates a React context with error handling for missing providers.
 *
 * @example
 * ```tsx
 * const [CounterProvider, useCounter] = createContext<{ count: number }>('Counter');
 * ```
 */
export function createContext<ContextValue>(
  rootComponentName: string,
  defaultContext?: ContextValue
) {
  const Context = createReactContext<ContextValue | undefined>(defaultContext);

  function Provider(props: React.PropsWithChildren<ContextValue>) {
    const { children, ...context } = props;
    // Cast to any to avoid TS errors when spreading context
    const value = context as unknown as ContextValue;
    return <Context.Provider value={value}>{children}</Context.Provider>;
  }

  function useContextHook(componentName?: string) {
    const context = useContext(Context);

    if (context === undefined && defaultContext === undefined) {
      throw new Error(
        \`\${componentName || 'Component'} must be used within a \${rootComponentName} provider\`
      );
    }

    return context as ContextValue;
  }

  Provider.displayName = \`\${rootComponentName}Provider\`;

  return [Provider, useContextHook] as const;
}
EOF

# Create test for create-context
cat > packages/core/src/__tests__/create-context.test.tsx << EOF
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import React from 'react';
import { createContext } from '../create-context';

describe('createContext', () => {
  it('should create a context provider and consumer hook', () => {
    const [CounterProvider, useCounter] = createContext<{ count: number }>('Counter');

    function Counter() {
      const { count } = useCounter('Counter');
      return <div data-testid="count">{count}</div>;
    }

    render(
      <CounterProvider count={42}>
        <Counter />
      </CounterProvider>
    );

    expect(screen.getByTestId('count').textContent).toBe('42');
  });

  it('should throw an error when consumer is used outside of provider', () => {
    const [, useCounter] = createContext<{ count: number }>('Counter');

    function Counter() {
      const { count } = useCounter('Counter');
      return <div>{count}</div>;
    }

    // Suppress console.error for this test
    const originalConsoleError = console.error;
    console.error = jest.fn();

    expect(() => render(<Counter />)).toThrow(
      'Counter must be used within a Counter provider'
    );

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('should use default context when provided', () => {
    const defaultContext = { count: 10 };
    const [, useCounter] = createContext<{ count: number }>('Counter', defaultContext);

    function Counter() {
      const { count } = useCounter('Counter');
      return <div data-testid="count">{count}</div>;
    }

    render(<Counter />);

    expect(screen.getByTestId('count').textContent).toBe('10');
  });
});
EOF
```

#### 2.4 Create ID Utility
```bash
# Create ID utility
cat > packages/core/src/use-id.ts << EOF
import { useState } from 'react';
import { useIsomorphicLayoutEffect } from './use-isomorphic-layout-effect';

// Counter for generating unique IDs
let count = 0;

/**
 * Generates a unique ID for accessibility purposes.
 * Falls back to React's useId when available.
 *
 * @example
 * ```tsx
 * const id = useId();
 * ```
 */
export function useId(prefix: string = 'lasensoft'): string {
  // Try to use React 18's built-in useId if available
  try {
    // @ts-ignore - React 18 feature
    return React.useId();
  } catch (e) {
    // Fall back to our implementation for React 17 and below
  }

  const [id, setId] = useState<string | null>(null);

  useIsomorphicLayoutEffect(() => {
    setId(\`\${prefix}-\${++count}\`);
  }, [prefix]);

  return id || '';
}
EOF

# Create isomorphic layout effect utility
cat > packages/core/src/use-isomorphic-layout-effect.ts << EOF
import { useLayoutEffect, useEffect } from 'react';

/**
 * A hook that uses useLayoutEffect in the browser and useEffect during SSR.
 * This avoids warnings when rendering on the server.
 */
export const useIsomorphicLayoutEffect =
  typeof window !== 'undefined' ? useLayoutEffect : useEffect;
EOF

# Create test for use-id
cat > packages/core/src/__tests__/use-id.test.tsx << EOF
import { describe, it, expect } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useId } from '../use-id';

describe('useId', () => {
  it('should generate a unique ID with default prefix', () => {
    const { result } = renderHook(() => useId());
    expect(result.current).toMatch(/^lasensoft-\d+$/);
  });

  it('should generate a unique ID with custom prefix', () => {
    const { result } = renderHook(() => useId('custom'));
    expect(result.current).toMatch(/^custom-\d+$/);
  });

  it('should generate different IDs for different instances', () => {
    const { result: result1 } = renderHook(() => useId());
    const { result: result2 } = renderHook(() => useId());

    expect(result1.current).not.toBe(result2.current);
  });
});
EOF
```

#### 2.5 Create Index File
```bash
# Create index file to export all utilities
cat > packages/core/src/index.ts << EOF
export * from './compose-event-handlers';
export * from './create-context';
export * from './use-controllable-state';
export * from './use-id';
export * from './use-isomorphic-layout-effect';
EOF
```

## Phase 3: Component Implementation Plan

### 1. Component Priority and Dependencies

#### 1.1 Component Dependency Graph
```
Primitive
├── VisuallyHidden
├── Portal
└── Slot
    ├── Button
    ├── Checkbox
    │   └── Switch
    ├── RadioGroup
    ├── AspectRatio
    ├── Separator
    ├── ScrollArea
    ├── Tabs
    ├── Accordion
    ├── Dialog
    │   └── Popover
    │       └── Tooltip
    └── Slider
```

#### 1.2 Implementation Order
1. **Foundational Components (Week 1)**
   - Primitive (base component)
   - VisuallyHidden
   - Portal
   - Slot

2. **Form Components (Week 2-3)**
   - Button
   - Checkbox
   - RadioGroup
   - Switch
   - Slider

3. **Layout Components (Week 3-4)**
   - AspectRatio
   - Separator
   - ScrollArea

4. **Navigation Components (Week 4-5)**
   - Tabs
   - Accordion

5. **Overlay Components (Week 5-6)**
   - Dialog
   - Popover
   - Tooltip

### 2. Component Implementation Template

#### 2.1 Directory Structure
For each component, create the following structure:
```
packages/react/[component-name]/
├── package.json
├── tsconfig.json
├── README.md
└── src/
    ├── index.ts
    ├── [component-name].tsx
    └── __tests__/
        └── [component-name].test.tsx
```

#### 2.2 Implementation Steps
For each component, follow this implementation pattern:

1. **Setup Component Package**
   - Create package.json with dependencies
   - Create TypeScript configuration
   - Create README with documentation

2. **Implement Component Logic**
   - Create component file with TypeScript types
   - Implement component functionality
   - Add accessibility features
   - Add keyboard navigation
   - Handle state management

3. **Write Component Tests**
   - Test rendering
   - Test user interactions
   - Test keyboard navigation
   - Test accessibility

4. **Create Documentation and Examples**
   - Add JSDoc comments
   - Create Storybook stories
   - Add usage examples

### 3. Component API Design Principles

#### 3.1 Naming Conventions
- Component names should be clear and descriptive
- Props should follow React conventions
- Event handlers should use `onEventName` pattern
- Boolean props should use `is` or `has` prefix when appropriate

#### 3.2 Component Structure
- Components should be implemented as function components with React.forwardRef
- Components should accept a ref that is forwarded to the underlying DOM element
- Components should spread remaining props to the underlying element
- Components should have a displayName for debugging

#### 3.3 State Management
- Components should support both controlled and uncontrolled modes
- State should be managed with useControllableState hook
- Complex state should use reducers when appropriate
- Context should be used for component composition

#### 3.4 Accessibility
- Components should include appropriate ARIA attributes
- Components should support keyboard navigation
- Components should have proper focus management
- Components should include screen reader announcements when needed

### 4. Detailed Component Specifications

#### 4.1 Primitive Component
- **Purpose**: Base component for building other components
- **Features**:
  - Forward refs to DOM elements
  - Support for polymorphic components (as prop)
  - Support for asChild pattern
- **Props**:
  - `as`: Element type to render
  - `asChild`: Whether to render children directly
  - All HTML attributes for the specified element

#### 4.2 Button Component
- **Purpose**: Interactive button element
- **Features**:
  - Support for different button types
  - Loading state
  - Disabled state
- **Props**:
  - `type`: Button type (button, submit, reset)
  - `disabled`: Whether the button is disabled
  - `loading`: Whether the button is in loading state
  - `loadingText`: Text to display when loading
  - All HTML button attributes

#### 4.3 Checkbox Component
- **Purpose**: Selectable input element
- **Features**:
  - Checked, unchecked, and indeterminate states
  - Support for form submission
  - Keyboard navigation
- **Props**:
  - `checked`: Whether the checkbox is checked
  - `defaultChecked`: Default checked state
  - `indeterminate`: Whether the checkbox is in indeterminate state
  - `onChange`: Callback when checked state changes
  - `disabled`: Whether the checkbox is disabled
  - `required`: Whether the checkbox is required
  - `name`: Name for form submission
  - `value`: Value for form submission
  - All HTML input attributes

#### 4.4 Dialog Component
- **Purpose**: Modal dialog
- **Features**:
  - Focus trapping
  - Keyboard navigation (Escape to close)
  - Backdrop click to close
  - Scroll locking
- **Props**:
  - `open`: Whether the dialog is open
  - `defaultOpen`: Default open state
  - `onOpenChange`: Callback when open state changes
  - `modal`: Whether the dialog is modal
  - All HTML div attributes

## Phase 4: Implementing Foundational Components

### 1. Primitive Component

#### 1.1 Setup Primitive Component Package
```bash
# Create primitive component directory structure
mkdir -p packages/react/primitive/src
mkdir -p packages/react/primitive/src/__tests__

# Create package.json
cat > packages/react/primitive/package.json << EOF
{
  "name": "@lasensoft/react-primitive",
  "version": "0.1.0",
  "license": "MIT",
  "source": "./src/index.ts",
  "main": "./src/index.ts",
  "module": "./src/index.ts",
  "publishConfig": {
    "main": "./dist/index.js",
    "module": "./dist/index.mjs",
    "types": "./dist/index.d.ts"
  },
  "files": [
    "dist",
    "README.md"
  ],
  "scripts": {
    "lint": "eslint --max-warnings 0 src",
    "lint:fix": "eslint --fix src",
    "clean": "rm -rf dist",
    "typecheck": "tsc --noEmit",
    "test": "vitest run",
    "test:watch": "vitest",
    "build": "tsup src/index.ts --format esm,cjs --dts"
  },
  "dependencies": {
    "@lasensoft/core": "workspace:*"
  },
  "peerDependencies": {
    "react": "^16.8 || ^17.0 || ^18.0"
  },
  "devDependencies": {
    "@lasensoft/test-utils": "workspace:*",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "tsup": "^6.7.0",
    "typescript": "^5.0.4",
    "vitest": "^0.30.1"
  }
}
EOF

# Create TypeScript configuration
cat > packages/react/primitive/tsconfig.json << EOF
{
  "extends": "../../../internal/tsconfig/react-library.json",
  "include": ["src"],
  "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.test.tsx"],
  "compilerOptions": {
    "outDir": "./dist"
  }
}
EOF

# Create README
cat > packages/react/primitive/README.md << EOF
# @lasensoft/react-primitive

A low-level primitive component for building other UI components.

## Installation

```bash
npm install @lasensoft/react-primitive
```

## Usage

```tsx
import { Primitive } from '@lasensoft/react-primitive';

// Use as a div
<Primitive.div>Content</Primitive.div>

// Use as a button
<Primitive.button onClick={() => console.log('clicked')}>
  Click me
</Primitive.button>

// Use with a different element type
<Primitive.div as="section">Section content</Primitive.div>

// Use with asChild to avoid wrapping children
<Primitive.div asChild>
  <div className="my-div">Content</div>
</Primitive.div>
```

## API

### Primitive.\{element\}

A component that renders the specified HTML element.

#### Props

- \`as\`: The element type to render. Defaults to the specified element.
- \`asChild\`: When true, the component will not render an extra DOM node and will only pass props to its child.
- All HTML attributes for the specified element are also accepted.
EOF
```

#### 1.2 Implement Primitive Component
```bash
# Create primitive component implementation
cat > packages/react/primitive/src/primitive.tsx << EOF
import React from 'react';

/**
 * Props for the Primitive component with ref forwarding
 */
export type PrimitivePropsWithRef<E extends React.ElementType> =
  React.ComponentPropsWithRef<E> & {
    /**
     * When true, the component will not render an extra DOM node
     * and will only pass props to its child.
     */
    asChild?: boolean;

    /**
     * The element type to render
     */
    as?: E;
  };

/**
 * Base primitive component that can be used to create other components
 */
function Primitive<E extends React.ElementType>(
  { asChild, as, ...props }: PrimitivePropsWithRef<E>,
  ref: React.Ref<any>
) {
  // If asChild is true, use Fragment to avoid rendering an extra DOM node
  // Otherwise, use the specified element type or default to 'div'
  const Comp = asChild ? React.Fragment : as || 'div';

  // Pass all props and ref to the component
  return <Comp {...props} ref={ref} />;
}

/**
 * Creates a primitive component for a specific HTML element
 */
const createPrimitive = <E extends React.ElementType>(displayName: string) => {
  // Create a component with ref forwarding
  const Component = React.forwardRef(Primitive<E>) as unknown as
    <T extends React.ElementType = E>(
      props: PrimitivePropsWithRef<T> & { as?: T }
    ) => React.ReactElement;

  // Set display name for debugging
  Component.displayName = displayName;
  return Component;
};

/**
 * Collection of primitive components for different HTML elements
 */
export const Primitive = {
  // Basic elements
  div: createPrimitive<'div'>('Primitive.div'),
  span: createPrimitive<'span'>('Primitive.span'),
  button: createPrimitive<'button'>('Primitive.button'),
  a: createPrimitive<'a'>('Primitive.a'),

  // List elements
  ul: createPrimitive<'ul'>('Primitive.ul'),
  ol: createPrimitive<'ol'>('Primitive.ol'),
  li: createPrimitive<'li'>('Primitive.li'),

  // Form elements
  form: createPrimitive<'form'>('Primitive.form'),
  input: createPrimitive<'input'>('Primitive.input'),
  label: createPrimitive<'label'>('Primitive.label'),
  select: createPrimitive<'select'>('Primitive.select'),
  textarea: createPrimitive<'textarea'>('Primitive.textarea'),

  // Heading elements
  h1: createPrimitive<'h1'>('Primitive.h1'),
  h2: createPrimitive<'h2'>('Primitive.h2'),
  h3: createPrimitive<'h3'>('Primitive.h3'),
  h4: createPrimitive<'h4'>('Primitive.h4'),
  h5: createPrimitive<'h5'>('Primitive.h5'),
  h6: createPrimitive<'h6'>('Primitive.h6'),

  // Other common elements
  p: createPrimitive<'p'>('Primitive.p'),
  section: createPrimitive<'section'>('Primitive.section'),
  article: createPrimitive<'article'>('Primitive.article'),
  aside: createPrimitive<'aside'>('Primitive.aside'),
  header: createPrimitive<'header'>('Primitive.header'),
  footer: createPrimitive<'footer'>('Primitive.footer'),
  nav: createPrimitive<'nav'>('Primitive.nav'),
  main: createPrimitive<'main'>('Primitive.main'),
};
EOF

# Create index file
cat > packages/react/primitive/src/index.ts << EOF
export * from './primitive';
EOF
```

#### 1.3 Write Primitive Component Tests
```bash
# Create test file for primitive component
cat > packages/react/primitive/src/__tests__/primitive.test.tsx << EOF
import React from 'react';
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Primitive } from '../primitive';

describe('Primitive', () => {
  it('renders a div by default', () => {
    render(<Primitive.div data-testid="test">Test</Primitive.div>);
    const element = screen.getByTestId('test');
    expect(element.tagName).toBe('DIV');
  });

  it('renders the specified element when using as prop', () => {
    render(<Primitive.div as="section" data-testid="test">Test</Primitive.div>);
    const element = screen.getByTestId('test');
    expect(element.tagName).toBe('SECTION');
  });

  it('forwards refs to the underlying element', () => {
    const ref = React.createRef<HTMLDivElement>();
    render(<Primitive.div ref={ref} data-testid="test">Test</Primitive.div>);
    expect(ref.current).not.toBeNull();
    expect(ref.current?.tagName).toBe('DIV');
  });

  it('passes props to the underlying element', () => {
    render(
      <Primitive.div
        data-testid="test"
        className="test-class"
        aria-label="test label"
      >
        Test
      </Primitive.div>
    );
    const element = screen.getByTestId('test');
    expect(element.className).toBe('test-class');
    expect(element.getAttribute('aria-label')).toBe('test label');
  });

  it('does not render an extra DOM node when asChild is true', () => {
    render(
      <Primitive.div asChild data-testid="test">
        <span>Test</span>
      </Primitive.div>
    );
    expect(screen.queryByTestId('test')).toBeNull();
    expect(screen.getByText('Test').tagName).toBe('SPAN');
  });

  it('renders different HTML elements correctly', () => {
    render(
      <>
        <Primitive.button data-testid="button">Button</Primitive.button>
        <Primitive.a data-testid="link" href="#">Link</Primitive.a>
        <Primitive.ul data-testid="list">
          <Primitive.li data-testid="list-item">Item</Primitive.li>
        </Primitive.ul>
      </>
    );

    expect(screen.getByTestId('button').tagName).toBe('BUTTON');
    expect(screen.getByTestId('link').tagName).toBe('A');
    expect(screen.getByTestId('list').tagName).toBe('UL');
    expect(screen.getByTestId('list-item').tagName).toBe('LI');
  });
});
EOF
```

### 2. VisuallyHidden Component

#### 2.1 Setup VisuallyHidden Component Package
```bash
# Create visually-hidden component directory structure
mkdir -p packages/react/visually-hidden/src
mkdir -p packages/react/visually-hidden/src/__tests__

# Create package.json
cat > packages/react/visually-hidden/package.json << EOF
{
  "name": "@lasensoft/react-visually-hidden",
  "version": "0.1.0",
  "license": "MIT",
  "source": "./src/index.ts",
  "main": "./src/index.ts",
  "module": "./src/index.ts",
  "publishConfig": {
    "main": "./dist/index.js",
    "module": "./dist/index.mjs",
    "types": "./dist/index.d.ts"
  },
  "files": [
    "dist",
    "README.md"
  ],
  "scripts": {
    "lint": "eslint --max-warnings 0 src",
    "lint:fix": "eslint --fix src",
    "clean": "rm -rf dist",
    "typecheck": "tsc --noEmit",
    "test": "vitest run",
    "test:watch": "vitest",
    "build": "tsup src/index.ts --format esm,cjs --dts"
  },
  "dependencies": {
    "@lasensoft/react-primitive": "workspace:*"
  },
  "peerDependencies": {
    "react": "^16.8 || ^17.0 || ^18.0"
  },
  "devDependencies": {
    "@lasensoft/test-utils": "workspace:*",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "tsup": "^6.7.0",
    "typescript": "^5.0.4",
    "vitest": "^0.30.1"
  }
}
EOF

# Create TypeScript configuration
cat > packages/react/visually-hidden/tsconfig.json << EOF
{
  "extends": "../../../internal/tsconfig/react-library.json",
  "include": ["src"],
  "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.test.tsx"],
  "compilerOptions": {
    "outDir": "./dist"
  }
}
EOF

# Create README
cat > packages/react/visually-hidden/README.md << EOF
# @lasensoft/react-visually-hidden

A component that visually hides content while keeping it accessible to screen readers.

## Installation

```bash
npm install @lasensoft/react-visually-hidden
```

## Usage

```tsx
import { VisuallyHidden } from '@lasensoft/react-visually-hidden';

// Hide content visually but keep it accessible to screen readers
<button>
  <VisuallyHidden>Close</VisuallyHidden>
  <XIcon />
</button>
```

## Accessibility

This component is designed to hide content visually while keeping it accessible to screen readers. This is useful for providing additional context to screen reader users without affecting the visual design.

## API

### VisuallyHidden

A component that visually hides its children while keeping them accessible to screen readers.

#### Props

All props are passed to the underlying span element.
EOF
```

#### 2.2 Implement VisuallyHidden Component
```bash
# Create visually-hidden component implementation
cat > packages/react/visually-hidden/src/visually-hidden.tsx << EOF
import React from 'react';
import { Primitive } from '@lasensoft/react-primitive';

/**
 * CSS styles to visually hide an element while keeping it accessible to screen readers
 */
const HIDDEN_STYLES = {
  border: 0,
  clip: 'rect(0 0 0 0)',
  height: '1px',
  margin: '-1px',
  overflow: 'hidden',
  padding: 0,
  position: 'absolute',
  whiteSpace: 'nowrap',
  width: '1px',
} as const;

/**
 * VisuallyHidden component props
 */
export type VisuallyHiddenProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;

/**
 * A component that visually hides content while keeping it accessible to screen readers.
 *
 * @example
 * ```tsx
 * <button>
 *   <VisuallyHidden>Close</VisuallyHidden>
 *   <XIcon />
 * </button>
 * ```
 */
export const VisuallyHidden = React.forwardRef<
  React.ElementRef<typeof Primitive.span>,
  VisuallyHiddenProps
>((props, forwardedRef) => (
  <Primitive.span
    {...props}
    ref={forwardedRef}
    style={{
      ...HIDDEN_STYLES,
      ...props.style,
    }}
  />
));

VisuallyHidden.displayName = 'VisuallyHidden';
EOF

# Create index file
cat > packages/react/visually-hidden/src/index.ts << EOF
export * from './visually-hidden';
EOF
```

#### 2.3 Write VisuallyHidden Component Tests
```bash
# Create test file for visually-hidden component
cat > packages/react/visually-hidden/src/__tests__/visually-hidden.test.tsx << EOF
import React from 'react';
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { VisuallyHidden } from '../visually-hidden';

describe('VisuallyHidden', () => {
  it('renders content that is accessible to screen readers', () => {
    render(<VisuallyHidden>Hidden text</VisuallyHidden>);
    expect(screen.getByText('Hidden text')).toBeInTheDocument();
  });

  it('applies visually hidden styles', () => {
    render(<VisuallyHidden data-testid="hidden">Hidden text</VisuallyHidden>);
    const element = screen.getByTestId('hidden');

    const styles = window.getComputedStyle(element);
    expect(styles.position).toBe('absolute');
    expect(styles.height).toBe('1px');
    expect(styles.width).toBe('1px');
    expect(styles.overflow).toBe('hidden');
  });

  it('forwards refs to the underlying element', () => {
    const ref = React.createRef<HTMLSpanElement>();
    render(<VisuallyHidden ref={ref}>Hidden text</VisuallyHidden>);
    expect(ref.current).not.toBeNull();
    expect(ref.current?.tagName).toBe('SPAN');
  });

  it('merges custom styles with visually hidden styles', () => {
    render(
      <VisuallyHidden
        data-testid="hidden"
        style={{ color: 'red', fontWeight: 'bold' }}
      >
        Hidden text
      </VisuallyHidden>
    );

    const element = screen.getByTestId('hidden');
    const styles = window.getComputedStyle(element);

    // Should have visually hidden styles
    expect(styles.position).toBe('absolute');
    expect(styles.height).toBe('1px');

    // Should also have custom styles
    expect(styles.color).toBe('red');
    expect(styles.fontWeight).toBe('bold');
  });

  it('passes other props to the underlying element', () => {
    render(
      <VisuallyHidden
        data-testid="hidden"
        className="custom-class"
        aria-label="hidden element"
      >
        Hidden text
      </VisuallyHidden>
    );

    const element = screen.getByTestId('hidden');
    expect(element.className).toBe('custom-class');
    expect(element.getAttribute('aria-label')).toBe('hidden element');
  });
});
EOF
```

### 3. Portal Component

#### 3.1 Setup Portal Component Package
```bash
# Create portal component directory structure
mkdir -p packages/react/portal/src
mkdir -p packages/react/portal/src/__tests__

# Create package.json
cat > packages/react/portal/package.json << EOF
{
  "name": "@lasensoft/react-portal",
  "version": "0.1.0",
  "license": "MIT",
  "source": "./src/index.ts",
  "main": "./src/index.ts",
  "module": "./src/index.ts",
  "publishConfig": {
    "main": "./dist/index.js",
    "module": "./dist/index.mjs",
    "types": "./dist/index.d.ts"
  },
  "files": [
    "dist",
    "README.md"
  ],
  "scripts": {
    "lint": "eslint --max-warnings 0 src",
    "lint:fix": "eslint --fix src",
    "clean": "rm -rf dist",
    "typecheck": "tsc --noEmit",
    "test": "vitest run",
    "test:watch": "vitest",
    "build": "tsup src/index.ts --format esm,cjs --dts"
  },
  "dependencies": {
    "@lasensoft/core": "workspace:*"
  },
  "peerDependencies": {
    "react": "^16.8 || ^17.0 || ^18.0",
    "react-dom": "^16.8 || ^17.0 || ^18.0"
  },
  "devDependencies": {
    "@lasensoft/test-utils": "workspace:*",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "tsup": "^6.7.0",
    "typescript": "^5.0.4",
    "vitest": "^0.30.1"
  }
}
EOF

# Create TypeScript configuration
cat > packages/react/portal/tsconfig.json << EOF
{
  "extends": "../../../internal/tsconfig/react-library.json",
  "include": ["src"],
  "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.test.tsx"],
  "compilerOptions": {
    "outDir": "./dist"
  }
}
EOF

# Create README
cat > packages/react/portal/README.md << EOF
# @lasensoft/react-portal

A component for rendering content outside the current DOM hierarchy.

## Installation

```bash
npm install @lasensoft/react-portal
```

## Usage

```tsx
import { Portal } from '@lasensoft/react-portal';

// Render content at the end of the document body
<Portal>
  <div className="modal">Modal content</div>
</Portal>

// Render content in a specific container
<Portal container={document.getElementById('modal-root')}>
  <div className="modal">Modal content</div>
</Portal>
```

## API

### Portal

A component that renders its children into a DOM node that exists outside the DOM hierarchy of the parent component.

#### Props

- \`container\`: The DOM node to render the children into. Defaults to document.body.
- \`children\`: The content to render in the portal.
EOF
```

#### 3.2 Implement Portal Component
```bash
# Create portal component implementation
cat > packages/react/portal/src/portal.tsx << EOF
import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { useIsomorphicLayoutEffect } from '@lasensoft/core';

/**
 * Portal component props
 */
export interface PortalProps {
  /**
   * The DOM node to render the children into.
   * Defaults to document.body.
   */
  container?: HTMLElement | null;

  /**
   * The content to render in the portal.
   */
  children: React.ReactNode;
}

/**
 * A component that renders its children into a DOM node that exists outside
 * the DOM hierarchy of the parent component.
 *
 * @example
 * ```tsx
 * <Portal>
 *   <div className="modal">Modal content</div>
 * </Portal>
 * ```
 */
export function Portal({ container, children }: PortalProps) {
  // State to track if we're in a browser environment
  const [mounted, setMounted] = useState(false);

  // Use a ref to store the container element
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);

  // Set mounted to true on the client side
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Set the portal container
  useIsomorphicLayoutEffect(() => {
    // Use the provided container or default to document.body
    setPortalContainer(container || document.body);
  }, [container]);

  // Only render the portal on the client side and when we have a container
  if (!mounted || !portalContainer) {
    return null;
  }

  // Create a portal to render children into the container
  return createPortal(children, portalContainer);
}

Portal.displayName = 'Portal';
EOF

# Create index file
cat > packages/react/portal/src/index.ts << EOF
export * from './portal';
EOF
```

#### 3.3 Write Portal Component Tests
```bash
# Create test file for portal component
cat > packages/react/portal/src/__tests__/portal.test.tsx << EOF
import React from 'react';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Portal } from '../portal';

describe('Portal', () => {
  let portalRoot: HTMLElement;

  beforeEach(() => {
    // Create a custom container for the portal
    portalRoot = document.createElement('div');
    portalRoot.setAttribute('id', 'portal-root');
    document.body.appendChild(portalRoot);
  });

  afterEach(() => {
    // Clean up the custom container
    document.body.removeChild(portalRoot);
  });

  it('renders children into the document body by default', () => {
    render(
      <Portal>
        <div data-testid="portal-content">Portal Content</div>
      </Portal>
    );

    // The portal content should be in the document
    const portalContent = screen.getByTestId('portal-content');
    expect(portalContent).toBeInTheDocument();

    // The portal content should be a child of the body, not the portal root
    expect(portalContent.parentElement).toBe(document.body);
  });

  it('renders children into the specified container', () => {
    render(
      <Portal container={portalRoot}>
        <div data-testid="portal-content">Portal Content</div>
      </Portal>
    );

    // The portal content should be in the document
    const portalContent = screen.getByTestId('portal-content');
    expect(portalContent).toBeInTheDocument();

    // The portal content should be a child of the portal root
    expect(portalContent.parentElement).toBe(portalRoot);
  });

  it('does not render when container is null', () => {
    render(
      <Portal container={null}>
        <div data-testid="portal-content">Portal Content</div>
      </Portal>
    );

    // The portal content should not be in the document
    expect(screen.queryByTestId('portal-content')).not.toBeInTheDocument();
  });

  it('updates the container when it changes', () => {
    const { rerender } = render(
      <Portal container={document.body}>
        <div data-testid="portal-content">Portal Content</div>
      </Portal>
    );

    // The portal content should be a child of the body
    let portalContent = screen.getByTestId('portal-content');
    expect(portalContent.parentElement).toBe(document.body);

    // Rerender with a different container
    rerender(
      <Portal container={portalRoot}>
        <div data-testid="portal-content">Portal Content</div>
      </Portal>
    );

    // The portal content should now be a child of the portal root
    portalContent = screen.getByTestId('portal-content');
    expect(portalContent.parentElement).toBe(portalRoot);
  });
});
EOF
```

### 4. Slot Component

#### 4.1 Setup Slot Component Package
```bash
# Create slot component directory structure
mkdir -p packages/react/slot/src
mkdir -p packages/react/slot/src/__tests__

# Create package.json
cat > packages/react/slot/package.json << EOF
{
  "name": "@lasensoft/react-slot",
  "version": "0.1.0",
  "license": "MIT",
  "source": "./src/index.ts",
  "main": "./src/index.ts",
  "module": "./src/index.ts",
  "publishConfig": {
    "main": "./dist/index.js",
    "module": "./dist/index.mjs",
    "types": "./dist/index.d.ts"
  },
  "files": [
    "dist",
    "README.md"
  ],
  "scripts": {
    "lint": "eslint --max-warnings 0 src",
    "lint:fix": "eslint --fix src",
    "clean": "rm -rf dist",
    "typecheck": "tsc --noEmit",
    "test": "vitest run",
    "test:watch": "vitest",
    "build": "tsup src/index.ts --format esm,cjs --dts"
  },
  "dependencies": {
    "@lasensoft/core": "workspace:*",
    "@lasensoft/react-primitive": "workspace:*"
  },
  "peerDependencies": {
    "react": "^16.8 || ^17.0 || ^18.0"
  },
  "devDependencies": {
    "@lasensoft/test-utils": "workspace:*",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "tsup": "^6.7.0",
    "typescript": "^5.0.4",
    "vitest": "^0.30.1"
  }
}
EOF

# Create TypeScript configuration
cat > packages/react/slot/tsconfig.json << EOF
{
  "extends": "../../../internal/tsconfig/react-library.json",
  "include": ["src"],
  "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.test.tsx"],
  "compilerOptions": {
    "outDir": "./dist"
  }
}
EOF

# Create README
cat > packages/react/slot/README.md << EOF
# @lasensoft/react-slot

A component for merging props onto its child element.

## Installation

```bash
npm install @lasensoft/react-slot
```

## Usage

```tsx
import { Slot } from '@lasensoft/react-slot';

// Merge props onto a child element
<Slot className="merged-class" onClick={() => console.log('clicked')}>
  <button className="original-class">Click me</button>
</Slot>

// The rendered output will be:
// <button class="original-class merged-class" onClick={() => console.log('clicked')}>Click me</button>
```

## API

### Slot

A component that merges its props onto its child element.

#### Props

All props are merged onto the child element.
EOF
```

#### 4.2 Implement Slot Component
```bash
# Create slot component implementation
cat > packages/react/slot/src/slot.tsx << EOF
import React, { isValidElement, cloneElement } from 'react';
import { composeEventHandlers } from '@lasensoft/core';

/**
 * Slot component props
 */
export interface SlotProps extends React.HTMLAttributes<HTMLElement> {
  /**
   * The content to render. Should be a single React element.
   */
  children: React.ReactNode;
}

/**
 * A component that merges its props onto its child element.
 *
 * @example
 * ```tsx
 * <Slot className="merged-class" onClick={() => console.log('clicked')}>
 *   <button className="original-class">Click me</button>
 * </Slot>
 * ```
 */
export const Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {
  const { children, ...slotProps } = props;

  // If children is not a valid element, return it as is
  if (!isValidElement(children)) {
    return <>{children}</>;
  }

  // Clone the child element and merge the props
  return cloneElement(children, {
    ...mergeProps(slotProps, children.props),
    ref: forwardedRef
      ? mergeRefs([forwardedRef, (children as any).ref])
      : (children as any).ref,
  });
});

Slot.displayName = 'Slot';

/**
 * Merges two sets of props, handling special cases like className and event handlers
 */
function mergeProps(
  slotProps: Record<string, any>,
  childProps: Record<string, any>
): Record<string, any> {
  const merged = { ...childProps };

  // Handle each slot prop
  for (const propName in slotProps) {
    // Skip children prop
    if (propName === 'children') continue;

    const slotPropValue = slotProps[propName];
    const childPropValue = childProps[propName];

    // Special case for className: concatenate them
    if (propName === 'className') {
      merged[propName] = [childPropValue, slotPropValue]
        .filter(Boolean)
        .join(' ');
    }
    // Special case for style: merge the objects
    else if (propName === 'style') {
      merged[propName] = { ...childPropValue, ...slotPropValue };
    }
    // Special case for event handlers: compose them
    else if (
      propName.startsWith('on') &&
      propName[2] === propName[2].toUpperCase() &&
      typeof slotPropValue === 'function' &&
      typeof childPropValue === 'function'
    ) {
      merged[propName] = composeEventHandlers(childPropValue, slotPropValue);
    }
    // Default case: use the slot prop
    else {
      merged[propName] = slotPropValue !== undefined ? slotPropValue : childPropValue;
    }
  }

  return merged;
}

/**
 * Merges multiple refs into a single ref function
 */
function mergeRefs<T>(refs: (React.Ref<T> | undefined)[]): React.RefCallback<T> {
  return (value) => {
    refs.forEach((ref) => {
      if (typeof ref === 'function') {
        ref(value);
      } else if (ref !== null && ref !== undefined) {
        (ref as React.MutableRefObject<T>).current = value;
      }
    });
  };
}
EOF

# Create index file
cat > packages/react/slot/src/index.ts << EOF
export * from './slot';
EOF
```

#### 4.3 Write Slot Component Tests
```bash
# Create test file for slot component
cat > packages/react/slot/src/__tests__/slot.test.tsx << EOF
import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Slot } from '../slot';

describe('Slot', () => {
  it('renders the child element', () => {
    render(
      <Slot>
        <button data-testid="button">Click me</button>
      </Slot>
    );

    expect(screen.getByTestId('button')).toBeInTheDocument();
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('merges className props', () => {
    render(
      <Slot className="slot-class">
        <button data-testid="button" className="button-class">Click me</button>
      </Slot>
    );

    const button = screen.getByTestId('button');
    expect(button.className).toBe('button-class slot-class');
  });

  it('merges style props', () => {
    render(
      <Slot style={{ color: 'red', fontSize: '16px' }}>
        <button data-testid="button" style={{ backgroundColor: 'blue' }}>Click me</button>
      </Slot>
    );

    const button = screen.getByTestId('button');
    const styles = window.getComputedStyle(button);

    expect(styles.color).toBe('red');
    expect(styles.fontSize).toBe('16px');
    expect(styles.backgroundColor).toBe('blue');
  });

  it('composes event handlers', () => {
    const slotClickHandler = vi.fn();
    const buttonClickHandler = vi.fn();

    render(
      <Slot onClick={slotClickHandler}>
        <button data-testid="button" onClick={buttonClickHandler}>Click me</button>
      </Slot>
    );

    const button = screen.getByTestId('button');
    fireEvent.click(button);

    expect(buttonClickHandler).toHaveBeenCalledTimes(1);
    expect(slotClickHandler).toHaveBeenCalledTimes(1);
  });

  it('forwards refs to the child element', () => {
    const ref = React.createRef<HTMLButtonElement>();

    render(
      <Slot ref={ref}>
        <button data-testid="button">Click me</button>
      </Slot>
    );

    expect(ref.current).not.toBeNull();
    expect(ref.current?.tagName).toBe('BUTTON');
    expect(ref.current).toBe(screen.getByTestId('button'));
  });

  it('merges refs when child already has a ref', () => {
    const slotRef = React.createRef<HTMLButtonElement>();
    const childRef = React.createRef<HTMLButtonElement>();

    render(
      <Slot ref={slotRef}>
        <button data-testid="button" ref={childRef}>Click me</button>
      </Slot>
    );

    expect(slotRef.current).not.toBeNull();
    expect(childRef.current).not.toBeNull();
    expect(slotRef.current).toBe(childRef.current);
    expect(slotRef.current).toBe(screen.getByTestId('button'));
  });

  it('handles non-element children', () => {
    render(
      <Slot>
        Text content
      </Slot>
    );

    expect(screen.getByText('Text content')).toBeInTheDocument();
  });
});
EOF
```

## Phase 5: Documentation and Testing Setup

### 1. Set Up Storybook
```bash
# Create storybook app directory structure
mkdir -p apps/storybook/.storybook
mkdir -p apps/storybook/src/stories

# Create package.json
cat > apps/storybook/package.json << EOF
{
  "name": "@lasensoft/storybook",
  "private": true,
  "version": "0.1.0",
  "scripts": {
    "dev": "storybook dev -p 9009",
    "build": "storybook build"
  },
  "dependencies": {
    "@lasensoft/react-primitive": "workspace:*",
    "@lasensoft/react-visually-hidden": "workspace:*",
    "@lasensoft/react-portal": "workspace:*",
    "@lasensoft/react-slot": "workspace:*"
  },
  "devDependencies": {
    "@storybook/addon-a11y": "^7.0.0",
    "@storybook/addon-actions": "^7.0.0",
    "@storybook/addon-docs": "^7.0.0",
    "@storybook/addon-essentials": "^7.0.0",
    "@storybook/addon-links": "^7.0.0",
    "@storybook/react": "^7.0.0",
    "@storybook/react-vite": "^7.0.0",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "storybook": "^7.0.0",
    "vite": "^4.0.0"
  }
}
EOF

# Create Storybook configuration
cat > apps/storybook/.storybook/main.ts << EOF
import { StorybookConfig } from '@storybook/react-vite';

const config: StorybookConfig = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|ts|tsx)'],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-a11y',
  ],
  framework: {
    name: '@storybook/react-vite',
    options: {},
  },
  docs: {
    autodocs: 'tag',
  },
};

export default config;
EOF

cat > apps/storybook/.storybook/preview.ts << EOF
import type { Preview } from '@storybook/react';

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
  },
};

export default preview;
EOF

# Create example stories
cat > apps/storybook/src/stories/Primitive.stories.tsx << EOF
import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { Primitive } from '@lasensoft/react-primitive';

const meta: Meta<typeof Primitive.div> = {
  title: 'Foundational/Primitive',
  component: Primitive.div,
  tags: ['autodocs'],
  argTypes: {
    as: {
      control: 'text',
      description: 'The element type to render',
    },
    asChild: {
      control: 'boolean',
      description: 'Whether to render children directly',
    },
    children: {
      control: 'text',
      description: 'The content to render',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Primitive.div>;

export const Default: Story = {
  args: {
    children: 'This is a primitive div',
  },
};

export const AsButton: Story = {
  args: {
    as: 'button',
    children: 'This is a button',
    onClick: () => alert('Button clicked'),
  },
};

export const WithClassName: Story = {
  args: {
    className: 'custom-class',
    style: { padding: '1rem', border: '1px solid #ccc', borderRadius: '4px' },
    children: 'This div has custom styling',
  },
};

export const AsChild: Story = {
  args: {
    asChild: true,
    children: <button>This button receives the primitive's props</button>,
  },
};
EOF

cat > apps/storybook/src/stories/VisuallyHidden.stories.tsx << EOF
import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { VisuallyHidden } from '@lasensoft/react-visually-hidden';

const meta: Meta<typeof VisuallyHidden> = {
  title: 'Foundational/VisuallyHidden',
  component: VisuallyHidden,
  tags: ['autodocs'],
  argTypes: {
    children: {
      control: 'text',
      description: 'The content to hide visually but keep accessible to screen readers',
    },
  },
};

export default meta;
type Story = StoryObj<typeof VisuallyHidden>;

export const Default: Story = {
  args: {
    children: 'This text is visually hidden but accessible to screen readers',
  },
  render: (args) => (
    <div>
      <p>The text below is visually hidden but accessible to screen readers:</p>
      <VisuallyHidden {...args} />
      <p>You can't see the hidden text, but screen readers will announce it.</p>
    </div>
  ),
};

export const WithButton: Story = {
  args: {
    children: 'Close',
  },
  render: (args) => (
    <button>
      <VisuallyHidden {...args} />
      <span aria-hidden="true">×</span>
    </button>
  ),
};
EOF
```

### 2. Set Up Testing Infrastructure
```bash
# Create test utilities directory structure
mkdir -p internal/test-utils/src

# Create package.json
cat > internal/test-utils/package.json << EOF
{
  "name": "@lasensoft/test-utils",
  "private": true,
  "version": "0.1.0",
  "main": "./src/index.ts",
  "scripts": {
    "typecheck": "tsc --noEmit"
  },
  "dependencies": {
    "@testing-library/jest-dom": "^5.16.5",
    "@testing-library/react": "^14.0.0",
    "@testing-library/user-event": "^14.0.0",
    "vitest": "^0.30.1"
  },
  "devDependencies": {
    "@types/testing-library__jest-dom": "^5.14.5",
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  }
}
EOF

# Create test setup file
cat > internal/test-utils/src/setup.ts << EOF
import '@testing-library/jest-dom';
EOF

# Create index file
cat > internal/test-utils/src/index.ts << EOF
export * from '@testing-library/react';
export * from '@testing-library/user-event';
export * from '@testing-library/jest-dom';

export * from './setup';
EOF

# Create custom test utilities
cat > internal/test-utils/src/custom-render.tsx << EOF
import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';

/**
 * Custom render function that wraps the component with providers if needed
 */
function customRender(
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) {
  return render(ui, {
    // Add providers here if needed
    // wrapper: ({ children }) => (
    //   <ThemeProvider>
    //     {children}
    //   </ThemeProvider>
    // ),
    ...options,
  });
}

export * from '@testing-library/react';
export { customRender as render };
EOF
```

## Phase 6: Implementation Schedule and Next Steps

### 1. Week-by-Week Implementation Plan

#### Week 1: Project Setup and Foundation
- **Day 1-2**: Set up project structure and configuration
  - Initialize monorepo structure
  - Configure package manager and workspaces
  - Set up TypeScript, ESLint, and Prettier
  - Create documentation files

- **Day 3-4**: Implement core utilities
  - Implement composeEventHandlers
  - Implement useControllableState
  - Implement createContext
  - Implement useId

- **Day 5**: Implement foundational components
  - Implement Primitive component
  - Implement VisuallyHidden component
  - Set up testing infrastructure

#### Week 2: Implement Portal and Slot Components
- **Day 1-2**: Implement Portal component
  - Create Portal component
  - Write tests for Portal component
  - Create Storybook stories for Portal component

- **Day 3-4**: Implement Slot component
  - Create Slot component
  - Write tests for Slot component
  - Create Storybook stories for Slot component

- **Day 5**: Set up Storybook and documentation
  - Configure Storybook
  - Create example stories
  - Document foundational components

#### Week 3: Implement Form Components
- **Day 1-2**: Implement Button component
  - Create Button component
  - Write tests for Button component
  - Create Storybook stories for Button component

- **Day 3-4**: Implement Checkbox component
  - Create Checkbox component
  - Write tests for Checkbox component
  - Create Storybook stories for Checkbox component

- **Day 5**: Implement RadioGroup component
  - Create RadioGroup component
  - Write tests for RadioGroup component
  - Create Storybook stories for RadioGroup component

#### Week 4: Implement More Form and Layout Components
- **Day 1-2**: Implement Switch and Slider components
  - Create Switch component
  - Create Slider component
  - Write tests for both components

- **Day 3-4**: Implement AspectRatio and Separator components
  - Create AspectRatio component
  - Create Separator component
  - Write tests for both components

- **Day 5**: Implement ScrollArea component
  - Create ScrollArea component
  - Write tests for ScrollArea component
  - Create Storybook stories for all layout components

#### Week 5: Implement Navigation Components
- **Day 1-3**: Implement Tabs component
  - Create Tabs component
  - Create TabsList, TabsTrigger, and TabsContent components
  - Write tests for Tabs components
  - Create Storybook stories for Tabs components

- **Day 4-5**: Implement Accordion component
  - Create Accordion component
  - Create AccordionItem, AccordionTrigger, and AccordionContent components
  - Write tests for Accordion components
  - Create Storybook stories for Accordion components

#### Week 6: Implement Overlay Components and Finalize
- **Day 1-2**: Implement Dialog component
  - Create Dialog component
  - Create DialogTrigger, DialogContent, DialogHeader, DialogFooter components
  - Write tests for Dialog components

- **Day 3-4**: Implement Popover and Tooltip components
  - Create Popover component
  - Create Tooltip component
  - Write tests for both components

- **Day 5**: Finalize documentation and testing
  - Complete component documentation
  - Ensure all tests pass
  - Set up CI/CD pipeline
  - Prepare for initial release

### 2. Continuous Integration and Deployment

#### 2.1 Set Up GitHub Actions Workflow
```bash
# Create GitHub Actions workflow directory
mkdir -p .github/workflows

# Create CI workflow
cat > .github/workflows/ci.yml << EOF
name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Install dependencies
        run: pnpm install

      - name: Lint
        run: pnpm lint

      - name: Type check
        run: pnpm types:check

      - name: Test
        run: pnpm test

      - name: Build
        run: pnpm build
EOF

# Create release workflow
cat > .github/workflows/release.yml << EOF
name: Release

on:
  push:
    branches: [main]

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Install dependencies
        run: pnpm install

      - name: Create Release Pull Request or Publish to npm
        id: changesets
        uses: changesets/action@v1
        with:
          publish: pnpm release
        env:
          GITHUB_TOKEN: \${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: \${{ secrets.NPM_TOKEN }}
EOF
```

### 3. Next Steps and Future Enhancements

#### 3.1 Additional Components to Consider
- **Data Display Components**
  - Table
  - Avatar
  - Badge
  - Progress
  - Card

- **Navigation Components**
  - Breadcrumb
  - Pagination
  - Menu
  - NavigationMenu

- **Feedback Components**
  - Alert
  - Toast
  - Skeleton
  - Progress

- **Media Components**
  - Image
  - Carousel
  - Video

#### 3.2 Future Enhancements
- **Theme System**
  - Implement a theme provider
  - Create design tokens
  - Support dark mode

- **Animation System**
  - Add animation utilities
  - Create transition components

- **Internationalization**
  - Add RTL support
  - Create utilities for internationalization

- **Documentation Site**
  - Create a dedicated documentation site
  - Add interactive examples
  - Provide API references

- **Performance Optimization**
  - Implement code splitting
  - Optimize bundle size
  - Add performance monitoring


