import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./internal/test-utils/src/setup.ts'],
    coverage: {
      reporter: ['text', 'json', 'html']
    }
  },
  resolve: {
    alias: {
      '@lasensoft/core': resolve(__dirname, 'packages/core/src'),
      '@lasensoft/theme': resolve(__dirname, 'packages/theme/src'),
      '@lasensoft/react': resolve(__dirname, 'packages/react'),
      '@lasensoft/react-primitive': resolve(__dirname, 'packages/react/atoms/primitive/src'),
      '@lasensoft/react-slot': resolve(__dirname, 'packages/react/slot/src'),
      '@lasensoft/react-utils': resolve(__dirname, 'packages/react/utils/src'),
      '@lasensoft/react-hooks': resolve(__dirname, 'packages/react/hooks/src'),
      '@lasensoft/react-portal': resolve(__dirname, 'packages/react/portal/src'),
      '@lasensoft/react-visually-hidden': resolve(__dirname, 'packages/react/visually-hidden/src')
    }
  }
});
