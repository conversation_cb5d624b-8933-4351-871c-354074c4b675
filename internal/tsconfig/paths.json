{"compilerOptions": {"baseUrl": ".", "paths": {"@lasensoft/core": ["../../packages/core/src"], "@lasensoft/core/*": ["../../packages/core/src/*"], "@lasensoft/react-primitive": ["../../packages/react/atoms/primitive/src"], "@lasensoft/react-primitive/*": ["../../packages/react/atoms/primitive/src/*"], "@lasensoft/react-utils": ["../../packages/react/utils/src"], "@lasensoft/react-utils/*": ["../../packages/react/utils/src/*"], "@lasensoft/react-slot": ["../../packages/react/slot/src"], "@lasensoft/react-slot/*": ["../../packages/react/slot/src/*"], "@lasensoft/react-hooks": ["../../packages/react/hooks/src"], "@lasensoft/react-hooks/*": ["../../packages/react/hooks/src/*"], "@lasensoft/react-portal": ["../../packages/react/portal/src"], "@lasensoft/react-portal/*": ["../../packages/react/portal/src/*"], "@lasensoft/react-visually-hidden": ["../../packages/react/visually-hidden/src"], "@lasensoft/react-visually-hidden/*": ["../../packages/react/visually-hidden/src/*"]}}}