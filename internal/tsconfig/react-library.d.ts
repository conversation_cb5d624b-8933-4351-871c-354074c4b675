// This file provides TypeScript types for the react-library configuration
// It can be referenced in tsconfig.json files that extend react-library.json

// Augment the JSX namespace to include custom elements
declare namespace JSX {
  interface IntrinsicElements {
    // Add any custom elements here if needed
  }
}

// Declare global types that should be available in all React libraries
interface Window {
  // Add any window extensions here if needed
}
