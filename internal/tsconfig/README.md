# @internal/tsconfig

Shared TypeScript configurations for the @lasensoft/primitives monorepo.

## Usage

### Base Configuration

```json
{
  "extends": "@internal/tsconfig/base.json"
}
```

### React Library Configuration

```json
{
  "extends": "@internal/tsconfig/react-library.json"
}
```

### App Configuration

```json
{
  "extends": "@internal/tsconfig/app.json"
}
```

### Next.js Configuration

```json
{
  "extends": "@internal/tsconfig/nextjs.json"
}
```

### Vite App Configuration

```json
{
  "extends": "@internal/tsconfig/vite-app.json"
}
```

### Vite Node Configuration

```json
{
  "extends": "@internal/tsconfig/vite-node.json"
}
```
